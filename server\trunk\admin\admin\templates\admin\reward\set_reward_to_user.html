{% extends 'admin/base.html' %}
{% block content %}
<script src="{{settings.MEDIA_URL}}/js/jquery-1.12.3.min.js" type="text/javascript"></script>
<script type='text/javascript' src='{{settings.BASE_URL}}/static/js/jquery-ui-1.12.1/jquery-ui.min.js'></script>
<link rel="stylesheet" href="{{settings.BASE_URL}}/static/js/jquery-ui-1.12.1/jquery-ui.min.css" type="text/css"/>
<script type='text/javascript' src='{{settings.MEDIA_URL}}/js/reward_content.js?v=22'></script>
<script type='text/javascript'>

    function chk(){
        var rewardType = $('input[name="reward_type"]:checked').val();

        if (rewardType == 'manual') {
            var userObj = document.getElementById('uids');
            if (userObj.value.length == 0){
                alert('请填写用户Id');
                userObj.focus();
                return false;
            }
        } else if (rewardType == 'zone') {
            var zoneList = document.getElementById('zone_list');
            if (zoneList.value.length == 0){
                alert('请填写区服列表');
                zoneList.focus();
                return false;
            }
        }
        /*
        var chkObj = document.getElementsByName("aid"); 
        var flag = true;
        for (var i = 0; i < chkObj.length; i++) { 
            if(chkObj[i].checked){
                flag = false; 
            }       
        } 
        var chkObj2 = document.getElementsByName("eid"); 
        for (var i = 0; i < chkObj2.length; i++) { 
            if(chkObj2[i].checked){
                flag = false; 
            }       
        } 
        */
        if ($('#name_{{nafo_list.0.0}}').val() == ''){
            alert('{{nafo_list.0.0}}标题不能为空');
            $('#name_{{nafo_list.0.0}}').focus();
            return false;
        }
        if ($('#info_{{nafo_list.0.0}}').val() == ''){
            alert('{{nafo_list.0.0}}正文描述不能为空');
            $('#info_{{nafo_list.0.0}}').focus();
            return false;
        }

        {%for item in input_list%}
        if($('#{{item.0}}').val()!=''){ 
            flag = false;
            if(parseInt($('#{{item.0}}').val()) != $('#{{item.0}}').val()){
                alert('请正确填写{{item.1}}数量');
                $('#{{item.0}}').focus();
            return false;
            }
            if(parseInt($('#{{item.0}}').val()) >parseInt({{item.2}})){
                alert('{{item.1}}数量不能超过{{item.2}}');
                $('#{{item.0}}').focus();
            return false;
            }
        }
        {%endfor%}






        return true;
    }
function show_zz(t){
    //var zz = jQuery("#zone").val();
    var zz = $("select option:selected").val();
    if (t==1){
        $('#zz_' + zz).show();
    }else{
        $('#uids_' + zz).val('')
        $('#zz_' + zz).hide()
    }
}

function toggleRewardType() {
    var rewardType = $('input[name="reward_type"]:checked').val();

    if (rewardType == 'manual') {
        $('#manual_content').show();
        $('#zone_content').hide();
        $('#filter_content').hide();
    } else if (rewardType == 'zone') {
        $('#manual_content').hide();
        $('#zone_content').show();
        $('#filter_content').show();
    } else if (rewardType == 'all') {
        $('#manual_content').hide();
        $('#zone_content').hide();
        $('#filter_content').show();
    }
}

function toggleFilters() {
    var enableFilters = $('#enable_filters').is(':checked');
    if (enableFilters) {
        $('#filter_options').show();
    } else {
        $('#filter_options').hide();
    }
}

$(document).ready(function() {
    toggleRewardType();
    toggleFilters();
});

</script>
<h2>邮件发奖 {%if reward_content.id%}编辑{%else%}添加{%endif%}</h2>
<div style="background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 10px; margin: 10px 0; border-radius: 5px;">
    <strong>📧 邮件发奖说明：</strong><br/>
    • 邮件发奖会给所有符合条件的注册用户发送邮件，包括当前离线的用户<br/>
    • 用户上线后可以在游戏内邮箱中查看和领取奖励<br/>
    • 支持全服发奖、指定区服发奖和条件过滤发奖<br/>
    • 条件过滤需要逐个检查用户信息，处理时间较长，请耐心等待
</div>
<form action="{{settings.BASE_URL}}/admin/reward/set_reward_to_user_p/"
    method="post" onsubmit="return chk();">
            <input type="submit" value="发送"/>
            <input id="rid" name="rid" type="hidden" value="{{reward_content.id}}" />

<table width="100%">
    <!-- 发奖类型选择 -->
    <tr>
        <td width="10%">发奖类型</td>
        <td>
            <input type="radio" name="reward_type" value="manual" checked onclick="toggleRewardType()"/> 手动输入UID
            <input type="radio" name="reward_type" value="zone" onclick="toggleRewardType()"/> 指定区服发奖（所有注册用户）
            <input type="radio" name="reward_type" value="all" onclick="toggleRewardType()"/> 全服发奖（所有注册用户）
        </td>
    </tr>

    <!-- 手动输入UID -->
    <tr id="manual_content">
        <td width="10%">用户UID</td>
        <td>
            <textarea id="uids" name="uids" style="width: 70%; height: 50px">{{ uids }}</textarea>
            (多用户用英文逗号分割，<font color="red">填写用户长uid</font>)
        </td>
    </tr>

    <!-- 区服选择 -->
    <tr id="zone_content" style="display:none;">
        <td width="10%">区服列表</td>
        <td>
            <textarea id="zone_list" name="zone_list" style="width: 70%; height: 50px"></textarea>
            (多区服用英文逗号分割，如：1,2,3)
            <br/>
            可选区服：
            {%for zone in zone_list%}
                <span style="margin-right: 10px;">{{zone.0}}({{zone.1}})</span>
            {%endfor%}
        </td>
    </tr>

    <!-- 条件过滤 -->
    <tr id="filter_content" style="display:none;">
        <td width="10%" style="vertical-align: top;">条件过滤</td>
        <td>
            <input type="checkbox" id="enable_filters" name="enable_filters" value="1" onclick="toggleFilters()"/> 启用条件过滤
            <div id="filter_options" style="display:none; margin-top: 10px; padding: 15px; border: 1px solid #ddd; background-color: #f9f9f9;">
                <table style="width: 100%;">
                    <tr>
                        <td style="width: 120px; padding: 5px;">府邸等级：</td>
                        <td style="padding: 5px;">
                            <input type="number" name="min_building_level" value="1" min="1" max="999" style="width: 60px;"/>
                            到
                            <input type="number" name="max_building_level" value="999" min="1" max="999" style="width: 60px;"/>
                            <span style="color: #666; margin-left: 10px;">（建筑001等级范围）</span>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 5px;">注册天数：</td>
                        <td style="padding: 5px;">
                            <input type="number" name="min_register_days" value="0" min="0" max="99999" style="width: 60px;"/>
                            到
                            <input type="number" name="max_register_days" value="99999" min="0" max="99999" style="width: 60px;"/>
                            <span style="color: #666; margin-left: 10px;">（注册多少天的玩家）</span>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 5px;">战力范围：</td>
                        <td style="padding: 5px;">
                            <input type="number" name="min_power" value="0" min="0" max="99999999" style="width: 80px;"/>
                            到
                            <input type="number" name="max_power" value="99999999" min="0" max="99999999" style="width: 80px;"/>
                            <span style="color: #666; margin-left: 10px;">（玩家战力范围）</span>
                        </td>
                    </tr>
                </table>
                <div style="margin-top: 10px; padding: 8px; background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404;">
                    <strong>注意：</strong>启用条件过滤会显著增加处理时间，因为需要逐个检查用户信息。建议在用户较少的区服使用。
                </div>
            </div>
        </td>
    </tr>
    <tr></tr>
    <tr></tr>
    <tr></tr>
    <tr></tr>
    <tr></tr>
    <tr></tr>
    <tr></tr>
    <tr></tr>
    <tr>
        <td>
            标题正文
        </td>
        <td>
         {%for item in nafo_list%}
         <tr>
             <td>
                 name-{{item.0}}
             </td>
             <td>
                 <input type="txt" name="name_{{item.0}}" id="name_{{item.0}}" value="{{item.1}}" /><br/>
             </td>
         </tr>
         <tr>
             <td>
                 info-{{item.0}}
             </td>
             <td>
                 <textarea id="info_{{item.0}}" name="info_{{item.0}}" style="width: 90%; height: 30px">{{item.2}}</textarea>
             </td>
         </tr>
         {%endfor%}

        </td>
    </tr> 
    <tr>
        <td>
        <td/>
        <td>
        <td/>
    <tr/>
        {%include 'admin/reward/reward_content.html'%}


</table>
</form>

{% endblock %}
