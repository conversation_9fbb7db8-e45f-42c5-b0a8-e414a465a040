{       
        'use_pk_yard_new': 0,    #比武大会模式   0旧/1新
        'use_city_control': 0,   #城池解锁模式（包含新手引导）   0民情/1城市接管
        'use_ng_task':0,     #政务模式，0旧/1新
        'use_star_new':0,     #星辰模式，0旧/1新（！后端不存！）
        'use_cabinet':0,     #辎重内阁，0旧/1新（！后端不存！）
        'use_milepost':0,     #天下大事儿，0旧/1新
        'use_newinstall':0,    #使用new_install3
        'use_shogun':0,     #是否使用新幕府 0旧/1新（！后端不存！）
        'use_drink':0,      #是否使用酒宴代替切磋  0旧/1新

        'country_flag':{     #城池上使用的旗子动画  通过城池使用的素材映射
		'map000':[0,0,1], # x,y,scale
		'map001':[0,0,1],
		'map001_':[0,0,1],
		'map002':[0,0,1],
		'map003':[0,0,1],
		'map003_':[0,0,1],
		'map004':[0,0,1],
		'map004_':[0,0,1],
		'map005':[0,0,1],
		'map005_':[0,0,1],
		'map006':[0,0,1],
		'map006_':[0,0,1],
		'map007':[0,0,1],
		'map007_':[0,0,1],
		'map008':[0,0,1],
		'map009':[0,0,1],
		'map010':[0,0,1],
		'map010_':[0,0,1],
		'map011':[0,0,1],
		'map011_':[0,0,1],
        },



        'init_server_ignore': {   #重启区服进程时是否加载超规用户数据， 服务器配置勿动1
            'switch': 1,          #功能开关
            'login_timeout': 30   #超过n天未登录的用户
        },
        'push_repeat': [5, 10],  #未收到客户端回执的推送重新发送的时间间隔，列表长度为重发次数
        'pf_pay_money': {

                           'pay0':1,   
                           'pay1': 6,
                           'pay2': 40,
                           'pay3': 93,
                           'pay4': 163,
                           'pay5': 418,
                           'pay6': 818,
                           'pay7': 1198,
                           'pay8': 2298,
                           'pay101': 12,
                           'pay102': 40,
                           'pay103': 118,
                           'pay104': 268,
                           'pay105': 388,
                           'pay219': 328,
        },     
        'auto_start':{
        	'user_limit':3000,	#单服导入人数
        	'time_limit':[[0,0],[5,0]],	#禁止开服时间
        	'longtime_limit':3*24*60,	#最长限制开服时间
        	'alert_limit':[4000,[13901390139,13801380138]],	#超过4000短信报警	
        }, 
        'drum_reward':{'merit':200},    #敲战鼓奖励
        'server_raise_pf': {'r2g_kr':'kr','r2g_kr_ad':'kr','r2g_kr_ios':'kr','r2g_kr_os':'kr','dev_kr':'kr'},
        'aes_key':{
                   '1':'ios_sg1_________',
                   '2':'asdfghjklzxcvbnm',
        },
        'coin_records_limit': [8,1000],    #官邸等级,每人消费记录条数

        'losttime_mail':21600,    #邮件有效分钟数21600
        'show_time_personal':10,     #私聊的时间显示间隔分钟
        'black_ip': [],    #黑名单ip
        'service_zone_list': [],    #维护分区列表d
        'service_info': [ 1, 'service_info','service_future', ['3012','3012','300','6010','6011','6012','6013','9994','8114','5200','*************','80908265','38','**************']],#a,b,c    a(0:维护，1:开服)，b（维护提示）c（白名单，支持uid and ip）**************
 #       'part_service_info': [], 
        'pay_sale': [                  #充值打折，折扣充值限额倒叙
                [50000,0.7],           #原始充值总额,折扣
                [20000,0.85],
                [10000,0.9],
                [400,0.95],
        ], 


              
        'building_lvup_reward':{3:{'merit':500},4:{'item088':1},6:{'food':500,'wood':1000,'iron':500},8:{'food':3400,'wood':2000,'iron':2200},10:{'iron':65000,'wood':9000,'food':6000,'item401':20},11:{'food':10000,'wood':4000,'iron':13000},15:{'item075':1},37:{'item2193':1,'food':500000,'wood':2000000,'iron':2000000}},         #官邸等级升级奖励
        'merge_reward':{
                'buildinglv_per':[[18,1],[12,0.6],[6,0.2],[1,0]],         #大于等于该等级
                'base_reward':{},
        },
        'auto_reply_bug_msg': [10,'190009','hero407'],#客服自动回复cd时间(分钟),文字id
        'zone_public_lv':{},         #k（分区id）,v（世界等级）, 当配置中存在k分区，则刷新世界等级取v
        'public_lv_0':[12,2,100,0.8],         #a初始等级，b每日增加等级，取排行榜前c名玩家官邸等级*3后的平均值（向上取整）*d参数     若世界等级小于该值则增加  c值必须小于等排行榜显示数量
        'public_lv_1':[70,2,100,0.8],         #a初始等级，b每日增加等级，取排行榜前c名玩家官邸等级*3后的平均值（向上取整）*d参数     若世界等级小于该值则增加  c值必须小于等排行榜显示数量
        'public_lv_2':[80,2,100,0.8],         #a初始等级，b每日增加等级，取排行榜前c名玩家官邸等级*3后的平均值（向上取整）*d参数     若世界等级小于该值则增加  c值必须小于等排行榜显示数量
        'public_lv_3':[90,2,100,0.8],         #a初始等级，b每日增加等级，取排行榜前c名玩家官邸等级*3后的平均值（向上取整）*d参数     若世界等级小于该值则增加  c值必须小于等排行榜显示数量
        'public_lv_4':[100,2,100,0.8],         #a初始等级，b每日增加等级，取排行榜前c名玩家官邸等级*3后的平均值（向上取整）*d参数     若世界等级小于该值则增加  c值必须小于等排行榜显示数量
        'public_lv_5':[110,2,100,0.8],         #a初始等级，b每日增加等级，取排行榜前c名玩家官邸等级*3后的平均值（向上取整）*d参数     若世界等级小于该值则增加  c值必须小于等排行榜显示数量
        'public_lv_6':[120,2,100,0.8],         #a初始等级，b每日增加等级，取排行榜前c名玩家官邸等级*3后的平均值（向上取整）*d参数     若世界等级小于该值则增加  c值必须小于等排行榜显示数量
        'public_lv_7':[130,2,100,0.8],         #a初始等级，b每日增加等级，取排行榜前c名玩家官邸等级*3后的平均值（向上取整）*d参数     若世界等级小于该值则增加  c值必须小于等排行榜显示数量
        'language': [['cn','简体中文','客服人员',['cn00','en00']],],   #语言,语言名称，客服人员名字,人声文件夹名称, ['en','English','serviceman'], ['tw','繁體中文','客服人員']  
	'pfs': [ 'ios_djcy','ios_mj1','ios_mjm1','ios_mjm2','ios_mjm5','ios', 'hw','hw_gzsgz','h5_37','37_ios','h5_twyx','h5_twyx2','h5_twyx3','h5_twyx4','h5_twyx5','h5_yyjh','h5_yyjh2','h5_7k','h5_leyou','h5_360','h5_muzhi','h5_muhi2','h5_1377','caohua','h5_ch','h5_ch2', 'test', 'test_zh', 'test_taptap', 'test_dx', 'test_uc','test_sogou1', 'test_sogou2', 'test_jrtt0', 'test_jrtt1', 'test_jrtt2', 'test_jrtt3', 'test_jrtt4', 'test_jrtt5', 'test_jrtt6', 'test_jrtt7', 'test_jrtt8', 'test_jrtt9','test_jrtt10', 'test_baidu1', 'test_baidu2', 'test_baidu3', 'test_baidu4', 'test_baidu5', 'test_tt1', 'test_tt2', 'test_tt3', 'test_tt4', 'test_tt5', 'test_tt6', 'test_tt7', 'test_tt8', 'test_tt9', 'test_tt10', 'test_tt11', 'test_tt12', 'test_tt13', 'test_tt14', 'test_tt15', 'test_tt16', 'test_tt17', 'test_tt18', 'test_tt19', 'test_tt20', 'test_tt21', 'test_tt22', 'test_tt23', 'test_tt24', 'test_tt25', 'test_tt26', 'test_tt27', 'test_tt28', 'test_tt29', 'test_tt30', 'test_tt31', 'test_tt32', 'test_tt33', 'test_tt34', 'test_tt35', 'test_tt36', 'test_tt37', 'test_tt38', 'test_tt39', 'test_tt40', 'test_tt41', 'test_tt42', 'test_tt43', 'test_tt44', 'test_tt45', 'test_tt46', 'test_tt47', 'test_tt48', 'test_tt49', 'test_tt50', 'test_clsgztt1', 'test_clsgztt2', 'test_clsgztt3', 'test_clsgztt4', 'test_clsgztt5', 'test_clsgztt6', 'test_clsgztt7', 'test_clsgztt8', 'test_clsgztt9', 'test_clsgztt10','uc', 'mi', 'oppo', 'vivo', 'mz','yyb', 'juedi', 'yqwb','h5_9130','h5_kuku','wende_ad','yx7477','h5_qq_ad','h5_qq_ios','h5_changwan','ios_37','h5_wende','h5_changxiang','ios_sh','cb_h5_37'], 
        'freeze_msgs': [
                unicode('您的账号存在异常，请联系QQ:3296460023', 'utf-8'),
                unicode('您有不当发言，请联系QQ:3296460023', 'utf-8'),
                unicode('您有不当发言，请联系QQ公众号：800869909', 'utf-8'),
                unicode('系统检测您存在违规言论，如有疑问请联系客服：800869909', 'utf-8'),
                unicode('您的账号存在违规操作，请点击悬浮窗内的线上客服进行沟通。', 'utf-8'),
        ],

	'app_version': '0.0', 
		'init_user':{
			'hero':[],
			'head':'hero411',
			'material':[0,150000,50000,50000,50000,0],    #升级需要消耗的[功勋，银币，粮草，木材，铁锭，元宝]
			'prop':[['item701',10],['item020',2],['item021',2],['item029',2],['item030',2],['item091',1]],
		},
		'init_user3':{
			'hero':[],
			'head':'hero411',
			'material':[0,150000,50000,50000,50000,0],    #升级需要消耗的[功勋，银币，粮草，木材，铁锭，元宝]
			'prop':[['item742',10],['item749',10],['item020',2],['item021',2],['item029',2],['item030',2],['item091',1]],
		},
        'npc_info_time':15,#斥候情报检查时间秒
        'order_check_time':15,#各种令检查时间秒
        'hero_buy':[10,50], #招募英雄需要的碎片数量 #超级将的碎片配置

        'solo_rate_name':[[0,191000,'#44FF44'],[0.25,191001,'#44CCFF'],[0.5,191002,'#FFAA44'],[0.75,191003,'#FF4444']],#兵损文字配置
        'move_time_name':[[0,191100,'#11BB11'],[120,191101,'#1166BB'],[300,191102,'#BB6611'],[900,191103,'#DD3311']],#距离文字配置

	'country_advise_init':[[0,0],[2.5,0.5],[1.5,3.5]],			#初始化三国的推荐分[魏,蜀,吴]   内部[0]人气分[1]壕气分  分值高的为削弱
	'country_advise_rate':[[1,1],[1.01,1],[1,1.01]],			#三国的推荐分系数，即除初始化外，各国的每次推荐分变动都乘以此系数[魏,蜀,吴]   内部[0]人气分[1]壕气分  分值高的为削弱

	'country_advise':[100,3,['coin',50]],			#已弃用[0]和[1]，[2]的奖励保留
        #玩家进区时，若该玩家为付过费老用户，推荐壕气积分最低国，否则推荐人气积分最低国
	'country_advise_entry':[1,2,5,-1],			        #当本国被推荐时，计入人气积分[0]；  当玩家加入推荐国时，推荐国再计入人气积分[1]；  当玩家加入非推荐国时，加入国计入人气积分[2]；  当玩家加入非推荐国时，原推荐国计入人气积分[3]（填负值）
	'country_advise_lv':{3:3,5:6,7:10,10:15},			#每当本国有玩家到达obj[k]级时，额外计入人气积分
	'country_advise_pay':{'pay1':[2,10],'pay2':[4,20],'pay3':[6,35],'pay4':[8,50],'pay5':[10,70],'pay6':[12,100],'pay7':[14,120],'pay8':[16,180]},		#每当本国有玩家成功充值时，额外计入[人气积分,壕气积分]
	'country_advise_check_lv':8,			                #当玩家升到n级时，检查该玩家账号信息
	'country_advise_max_lv':[[24,25],[21,20],[18,15],[15,10],[12,5]],			#所有区最高等级>=arr[i][0]，额外计入人气积分arr[i][1]
	'country_advise_all_pay':[[100000,25,140],[30000,25,120],[8000,25,100],[2000,20,80],[500,15,60],[100,10,40],[6,5,20]],		#总计真实充值>=arr[i][0]，额外计入人气积分arr[i][1]和壕气积分arr[i][2]

        'king_img':["hero762","hero763","hero764"],#魏蜀吴国王的英雄id
        'country_task_img':["hero762","hero763","hero764"],#国家界面用的素材

	'rename_item':'item091',                  #改名卡道具id，使用后可改名
	'hero_exp': [      #升级英雄经验，【等级段】，【a，b】，经验公式为ax+b x为英雄等级 等级上限是官邸*3
	
	        [2,10, 50],   #从1升2及的经验
	        [5,10, 130], #从2升3至4升5的经验 
	        [10,15, 165], #从5升6至9升10的经验 
	        [20,20, 420],#从10升11至19升20的经验
	        [25,30, 630],
	        [30,30, 930],
	        [35,40, 1240],
	        [40,40, 1640],
	        [45,50, 2050],
	        [50,50, 2550],
	        [55,70, 3570],
	        [81,70, 4270],
	        [90,90, 5000],
	        [99,5000, -355000],
	        [108,5000, -355000],
	        [117,5000, -355000],
	        [126,5000, -355000],
	            ], 
       'hero_tip_level':0.8, #只有等级达到最强英雄80%等级的英雄才有资格在英雄排列界面显示提示红点
       'level_limit':[3,0], #等级限制系数[a,b]官邸的等级*a+b 
	
	
	'hero_star':          #【突破消耗的英雄碎片，消耗的gold，快速突破消耗的coin】
	
	           [[10,10000], [10,20000], [10,30000], [10,40000], [10, 50000], [50, 80000],[20, 110000], [20, 120000], [20, 130000], [20, 140000], [20, 150000], [100, 200000],  [40, 300000], [50, 400000], [60, 500000], [70, 600000], [80, 700000], [200, 1500000], [100, 2000000], [100, 2000000], [100, 2000000], [100, 2000000], [100, 2000000]],
	
	'super_hero_star':          #【突破消耗的英雄碎片，消耗的gold，快速突破消耗的coin】
	
	           [[10,100000], [20,150000], [30,200000], [40,250000], [50, 300000], [100, 1000000],[30, 200000], [40, 250000], [50, 300000], [60, 350000], [70, 400000], [200, 2000000],  [50, 500000], [60, 600000], [70, 700000], [80, 800000], [90, 900000], [400, 4000000], [100, 2000000], [100, 2000000], [100, 2000000], [100, 2000000], [100, 2000000]],	
	             
	'star_limit_init':5,# 突破特权限制 
	'star_revived':[1,1,1,1,1],   #一次转生对应一个突破上限
	'star_revived_limit':18,   #英雄小于18星，转生增加的上限不生效
      
      'barracks':{  #训练10个兵的时间单位秒和每次训练制造上限，库存上限和兵种等级
               'building009':[
[6,1000,5000,1],[5.88,1100,5500,1],[5.76,1200,6000,1],[5.65,1300,6500,1],[5.53,1400,7000,1],[6.51,1600,7500,2],[6.38,1800,8000,2],[6.25,2000,9500,2],[6.13,2300,11000,2],[6,2600,12500,2],[5.88,2900,14000,2],[6.86,3200,15500,3],[6.73,3500,17000,3],[6.59,3800,20000,3],[6.46,4200,25000,3],[6.33,4600,30000,3],[6.2,5000,35000,3],[6.95,5500,40000,4],[6.81,6000,50000,4],[6.67,6500,60000,4],[6.54,7000,70000,4],[6.41,7500,80000,4],[6.28,8000,90000,4],[5.53,9000,100000,5],[5.36,10000,110000,5],[5.2,11000,120000,5],[5.04,12000,130000,5],[4.89,13000,140000,5],[4.75,14000,150000,5],[4.75,15000,160000,5],

],
                  
               'building010':[
[6,1000,5000,1],[5.88,1100,5500,1],[5.76,1200,6000,1],[5.65,1300,6500,1],[5.53,1400,7000,1],[6.51,1600,7500,2],[6.38,1800,8000,2],[6.25,2000,9500,2],[6.13,2300,11000,2],[6,2600,12500,2],[5.88,2900,14000,2],[6.86,3200,15500,3],[6.73,3500,17000,3],[6.59,3800,20000,3],[6.46,4200,25000,3],[6.33,4600,30000,3],[6.2,5000,35000,3],[6.95,5500,40000,4],[6.81,6000,50000,4],[6.67,6500,60000,4],[6.54,7000,70000,4],[6.41,7500,80000,4],[6.28,8000,90000,4],[5.53,9000,100000,5],[5.36,10000,110000,5],[5.2,11000,120000,5],[5.04,12000,130000,5],[4.89,13000,140000,5],[4.75,14000,150000,5],[4.75,15000,160000,5],

],

               'building011':[[6,1000,5000,1],[5.88,1100,5500,1],[5.76,1200,6000,1],[5.65,1300,6500,1],[5.53,1400,7000,1],[6.51,1600,7500,2],[6.38,1800,8000,2],[6.25,2000,9500,2],[6.13,2300,11000,2],[6,2600,12500,2],[5.88,2900,14000,2],[6.86,3200,15500,3],[6.73,3500,17000,3],[6.59,3800,20000,3],[6.46,4200,25000,3],[6.33,4600,30000,3],[6.2,5000,35000,3],[6.95,5500,40000,4],[6.81,6000,50000,4],[6.67,6500,60000,4],[6.54,7000,70000,4],[6.41,7500,80000,4],[6.28,8000,90000,4],[5.53,9000,100000,5],[5.36,10000,110000,5],[5.2,11000,120000,5],[5.04,12000,130000,5],[4.89,13000,140000,5],[4.75,14000,150000,5],[4.75,15000,160000,5],

],

               'building012':[[6,1000,5000,1],[5.88,1100,5500,1],[5.76,1200,6000,1],[5.65,1300,6500,1],[5.53,1400,7000,1],[6.51,1600,7500,2],[6.38,1800,8000,2],[6.25,2000,9500,2],[6.13,2300,11000,2],[6,2600,12500,2],[5.88,2900,14000,2],[6.86,3200,15500,3],[6.73,3500,17000,3],[6.59,3800,20000,3],[6.46,4200,25000,3],[6.33,4600,30000,3],[6.2,5000,35000,3],[6.95,5500,40000,4],[6.81,6000,50000,4],[6.67,6500,60000,4],[6.54,7000,70000,4],[6.41,7500,80000,4],[6.28,8000,90000,4],[5.53,9000,100000,5],[5.36,10000,110000,5],[5.2,11000,120000,5],[5.04,12000,130000,5],[4.89,13000,140000,5],[4.75,14000,150000,5],[4.75,15000,160000,5],

],
                  
	
	

	     },

       'fast_train_pay':500,   #充值1000RMB和以上的玩家可以看到快速训练按钮
       'fast_train_type':'coin',   #充值1000RMB和以上的玩家可以看到快速训练按钮
       'fast_train_cost':[360,360,360], #用1元宝购买资源的数量，向下取整扣元宝，对应粮食，木材，生铁
       'fast_num':[2.03,2.02,2.01,1.99,1.98,2.01,2,1.98,1.97,1.96,1.95,1.98,1.96,1.95,1.94,1.93,1.92,1.89,1.88,1.87,1.86,1.85,1.84,1.73,1.72,1.71,1.7,1.69,1.68,1.5,],
   #对应的兵营等级系数，辅助计算使用，计算对应粮草，木材，生铁时都要乘对应系数

       'fast_level':[[100000,1],[200000,1],[300000,1],[400000,1],[500000,1],[600000,1],[700000,1],[800000,1],[900000,1],[1000000,1],[1500000,1],[2000000,1],[2500000,1],[3000000,1],[3500000,1],[4000000,1],[4500000,1],[5000000,1],[5500000,1],[6000000,1],[6500000,1],[7000000,1],[7500000,1],[8000000,1],[8500000,1],[9000000,1],[9500000,1],[10000000,1],[15000000,1],[20000000,1],[25000000,1],[30000000,1],[35000000,1],[40000000,1],



],
   #记录当天在一键补兵上消耗的总粮草数量，如果达到对应阶段则消耗再乘以对应倍数

	
	'exp_book':{'item001':500, 'item002':2000, 'item003':5000,'item004': 10000}, #技能书配置{'item002':500, 'item003':2000, 'item004':5000,'item005': 1000}
	

	
	'material_gift':[500,700,900,1100,1300,1500,1700,1900,2100,2300,2500,2700,2900,3100,3300,3500,3700,3900,4100,4300,4500,4700,4900,5100,5300,5500,5700,5900,6100,6300,6500,6700,6900,7100,7300,7500,7700,7900,8100,8300,8500,8700],   #资源基础产出随等级变化值,42个等级
        'material_gift_cd':[60,60],#资源基础产出的cd,分钟(大地图,封地)
        'material_gift_limit':[48,48],#资源基础存储周期上线(大地图,封地)
	'material_scale':[1,2,2,2],         #钱，粮，木，铁  material_gift x material_scale
	'cd_cost':[1,1,2,1,1,1,1],             #[升级,宝物,科技，训练，产业，建造，拜访] 花费coin=剩余分钟/对应功能比例值
        'material_all_get':0,#封地钱粮木铁一键全收
######################加速道具##############
	'item_cd':[                      #用道具减少cd
		{'item020':10,'item021':60},	#建筑道具,产业//分钟
		{'item020':10,'item021':60},	#宝物突破道具//分钟
		{'item023':10,'item024':60,'item020':10,'item021':60},	#科技道具	
		{'item026':10,'item027':60,'item020':10,'item021':60},	#募兵道具
		{'item020':10,'item021':60},	#产业主动行为//分钟
	],
	'item_army_go':[['item029',0.25,25],['item030',0.5,50]], #[道具id,剩余时间*(1-参b),直接用黄金]
########################################################
#########购买资源相关######################
########################################################
	'baggage':{
		'fresh_time':[[5,0],[17,00]],   #每日购买次数归0时间
		'use_coin':[5,5,10,10,15,15,20,20,25,25,30,30,35,35,40,40,45,45,50,50,55,55,60,60,65,65,70,70,75,75,80,80,95,95,100,100,105,105,110,110,115,115,120,120,125,125,130,130,135,135,140,140,145,145,150,150],   #次数穷举数组，如果不够位数，容错为最后一位循环
		'limit':[10,2],       #购买上限为a+辎重站等级*b
		'free_buy':0,         #基础免费次数
		'buy_gold':[7500,200,200,25,0.4,1.5,],      #a+(b*building_lv)+(c+(building_lv*d))*当前付费次数 [a,b,c,d,e,f]  a基础资源数，b每升一级辎重站增长的资源数，每购买一次增长的资源数=c基础+辎重站等级*d倍数，购买时的暴击几率，暴击时获得的倍率
		'buy_food':[15000,400,400,50,0.4,1.5,],
		'buy_wood':[15000,400,400,50,0.4,1.5,],
		'buy_iron':[15000,400,400,50,0.4,1.5,],   #'item701',5
	},	


########################################################
#########技能杂项######################
########################################################
    'skill_forget':['item035',0.5],  #技能遗忘比例 向上取整 ,遗忘分,初始/非初始
    'skill_cost':{#cost_type,[碎片,gold],只有学的时候用coin(fast_learn)
	
	              '0':[[5,5000],[5,10000],[5,15000],[10,20000],[10,25000],[10,30000],[15,35000],[20,40000],[25,45000],[30,50000],[40,55000],[50,60000],[60,65000],[70,70000],[80,75000],[100,80000],[120,85000],[140,90000],[160,95000],[200,100000],[250,150000],[300,200000],[350,250000],[400,300000],[500,350000],[300,450000],[500,550000],[700,650000],[900,750000],[1200,850000],],



	
	              '1':[[5,8000],[5,16000],[5,24000],[10,32000],[10,40000],[10,48000],[15,56000],[20,64000],[25,72000],[30,80000],[40,88000],[50,96000],[60,104000],[70,112000],[80,120000],[100,128000],[120,136000],[140,144000],[160,152000],[200,160000],[250,240000],[300,320000],[350,400000],[400,480000],[500,560000],[300,720000],[500,880000],[700,1040000],[900,1200000],[1200,1360000],],


	
	              '2':[[5,10000],[5,20000],[5,30000],[10,40000],[10,50000],[10,60000],[15,70000],[20,80000],[25,90000],[30,100000],[40,110000],[50,120000],[60,130000],[70,140000],[80,150000],[100,160000],[120,170000],[140,180000],[160,190000],[200,200000],[250,300000],[300,400000],[350,500000],[400,600000],[500,700000],[300,900000],[500,1100000],[700,1300000],[900,1500000],[1200,1700000],],


                      
                      '3':[[5,12000],[5,24000],[5,36000],[10,48000],[10,60000],[10,72000],[15,84000],[20,96000],[25,108000],[30,120000],[40,132000],[50,144000],[60,156000],[70,168000],[80,180000],[100,192000],[120,204000],[140,216000],[160,228000],[200,240000],[250,360000],[300,480000],[350,600000],[400,720000],[500,840000],[300,1080000],[500,1320000],[700,1560000],[900,1800000],[1200,2040000],],



                       
                      '4':[[5,10000],[5,20000],[5,30000],[10,40000],[10,50000],[10,60000],[15,70000],[20,80000],[25,90000],[30,100000],[40,110000],[50,120000],[60,130000],[70,140000],[80,150000],[100,160000],[120,170000],[140,180000],[160,190000],[200,200000],[300,900000],[500,1100000],[700,1300000],[900,1500000],[1200,1700000],],



	
                      '5':[[5,10000],[5,15000],[5,20000],[10,25000],[10,30000],[10,35000],[15,40000],[20,45000],[25,50000],[30,55000],[40,60000],[50,65000],[60,70000],[70,75000],[80,80000],[100,85000],[120,90000],[140,95000],[160,100000],[200,105000],[300,900000],[500,1100000],[700,1300000],[900,1500000],[1200,1700000],],



                       
                     '6':[[10,40000],[30,80000],[50,120000],[100,160000],[200,200000],[300,240000],[300,900000],[500,1100000],[700,1300000],[900,1500000],[1200,1700000],],





	
	},    #技能升级消耗的碎片和银币数量


     'skill_lv_science':[['117',1],['118',1],['119',1],['120',1],['122',1],['123',1],],#科技提升技能等级上限，对应技能type

     'skill_cost_max_lv':{'7':125,'8':250},    #副将等特殊技能满级

     'skill_cost_max_lv_science':{'7':[['124',5]],'8':[['125',10],['134',10]]},    #科技提升副将技能等级上限


     'color_info':['_public196','_public197','_public165','_public166','_public167','_public168'],              #品质颜色文字，白绿蓝紫金红
     'skill_color':{#cost_type,等级颜色
                        '0':[1,4,7,10,13,17,26],#对应白绿蓝紫金红粉  最低级兵种技
                        '1':[1,4,7,10,13,17,26],#对应白绿蓝紫金红粉  低级兵种技
                        '2':[1,4,7,10,13,17,26],#对应白绿蓝紫金红粉  中级兵种技
                        '3':[1,4,7,10,13,17,26],#对应白绿蓝紫金红粉  高级兵种技  英雄技
                        '4':[1,4,7,10,13,17,21],#对应白绿蓝紫金红粉  内政技
                        '5':[1,4,7,10,13,17,21],#对应白绿蓝紫金红粉  20级辅助技
                        '6':[1,2,3,4,5,6,7],   #对应白绿蓝紫金红粉  6级辅助技
                        '7':[0,1,20,40,60,90,126],   #对应白绿蓝紫金红粉     副将英雄技，满级125
                        '8':[0,1,40,80,120,180,251],   #对应白绿蓝紫金红粉     副将兵种技，满级250

            },

     #英雄等级上限0:72, 1:81, 2:90, 3:99, 4:99, 5:108, 6:117, 7:126
     #技能等级，技能解锁
     'skill_bag_limit': {  #对应稀有度rarity0,1,2,3
	    '0':['level',[0,0,30,45,60,80,93,100,120]], #步兵技能栏限制，等级解锁
	    '1':['level',[0,0,30,45,60,80,93,100,120]],
	    '2':['level',[0,0,15,35,55,80,97,105,125]],
	    '3':['level',[0,0,15,35,55,80,97,105,125]],
	    '4':['star',[0,6,12,18]],#英雄技能 用突破等级限锁
	    '5':['level',[0,0,12,27,42,57,72,87,110]],#辅助技能
	    '6':['level',[0,0,0,15,20,25,30,35]],#内政技能
     },
     'skill_bag_limit_super': {    #对应稀有度rarity4  传奇英雄
	    '0':['level',[0,0,30,45,60,80,93,100,120]], #步兵技能栏限制，等级解锁
	    '1':['level',[0,0,30,45,60,80,93,100,120]],
	    '2':['level',[0,0,15,35,55,80,97,105,125]],
	    '3':['level',[0,0,15,35,55,80,97,105,125]],
	    '4':['level',[0,20,40,60]],#英雄技能 用等级限锁
	    '5':['level',[0,0,12,27,42,57,72,87,110]],#辅助技能
	    '6':['level',[0,0,0,15,20,25,30,35]],#内政技能
     },

     'skill_lv_limit': [0,0,0,0,0,0,0,0,5,7,9,11,13,16,18,20,22,23,24,25],#技能等级对应PVE章节
#######################
#######宝物杂项##########
#######################
         'equip_make_list':{
            '0':[1,[ 'equip001','equip002','equip003','equip004','equip005','equip006','equip007','equip008','equip009','equip010','equip011'],{'iron':60000,'item401':20}],      #珍宝阁等级限制，神兵列表,制作一次消耗的资源
            '1':[1,[ 'equip012','equip013','equip014','equip015','equip016','equip017',],{'food':60000,'item402':20}],      #珍宝阁等级限制，坐骑列表,制作一次消耗的资源
            '2':[1,[ 'equip019','equip020','equip021','equip022','equip023','equip024'],{'wood':60000,'item403':20}],      #珍宝阁等级限制，奇珍列表,制作一次消耗的资源
            '3':[1,[ 'equip025','equip026','equip027','equip028','equip029','equip030',],{'gold':30000,'item404':20}],      #珍宝阁等级限制，宝典列表,制作一次消耗的资源
            '4':[4,[ 'equip031','equip032','equip033',],{'wood':60000,'iron':60000,'item405':40}],      #珍宝阁等级限制，令符列表,制作一次消耗的资源


                     },
         'equip_make_special':[3,['equip052','equip034','equip038','equip041','equip043','equip044','equip046','equip048','equip045','equip064','equip077','equip078','equip079','equip080','equip081','equip082','equip083','equip084','equip085','equip086','equip087','equip088','equip089','equip090','equip091','equip092','equip042','equip049','equip051','equip018','equip035','equip098','equip099','equip101','equip103','equip106','equip104','equip105','equip107','equip040','equip123','equip122','equip121','equip120','equip119','equip134','equip135','equip136','equip137','equip138','equip139','equip140','equip141','equip142','equip143','equip149','equip150','equip151','equip152','equip153','equip154','equip168','equip162','equip157','equip158','equip159','equip160','equip161','equip163','equip164','equip165','equip166','equip167','equip169','equip170','equip171','equip172','equip173','equip174','equip175','equip176','equip177','equip178','equip179','equip180','equip181','equip182','equip183','equip184','equip185','equip186','equip187','equip188','equip189','equip190','equip191','equip192','equip193','equip195']], #珍宝阁等级限制，令符列表,制作一次消耗的资源

         'equip_make_normal':['item401','item402','item403','item404','item405',], #[4,[]],珍宝阁等级限制，令符列表,制作一次消耗的资源

        'equip_upgrade_fail':0.5,#宝物升级失败的资源返还比例
      
        'equip_property_box':{
                          '0':{},
                          '1':{},
                          '2':{},


                         },
        'equip_wash_box':[

[99,[[['wash005',1],11],[['wash010',1],11],[['wash015',1],11],[['wash020',1],11],[['wash021',11],1333],[['wash022',10],1000],[['wash023',8],666],[['wash024',2],55],[['wash025',1],11],[['wash026',11],1333],[['wash027',10],1000],[['wash028',8],666],[['wash029',2],55],[['wash030',1],11],[['wash031',11],1333],[['wash032',10],1000],[['wash033',8],666],[['wash034',2],55],[['wash035',1],11],[['wash036',11],1333],[['wash037',10],1000],[['wash038',8],666],[['wash039',2],55],[['wash040',1],11],[['wash041',11],1333],[['wash042',10],1000],[['wash043',8],666],[['wash044',2],55],[['wash045',1],11],[['wash046',11],1333],[['wash047',10],1000],[['wash048',8],666],[['wash049',2],55],[['wash050',1],11],[['wash051',11],1333],[['wash052',10],1000],[['wash053',8],666],[['wash054',2],55],[['wash055',1],11],[['wash058',8],666],[['wash059',2],55],[['wash060',1],11],[['wash061',11],1333],[['wash062',10],1000],[['wash063',8],666],[['wash064',2],55],[['wash065',1],11],[['wash066',11],1333],[['wash067',10],1000],[['wash068',8],666],[['wash069',2],55],[['wash070',1],11],[['wash071',11],1333],[['wash072',10],1000],[['wash073',8],666],[['wash074',2],55],[['wash075',1],11],[['wash076',11],1333],[['wash077',10],1000],[['wash078',8],666],[['wash079',2],55],[['wash080',1],11],[['wash081',11],1333],[['wash082',10],1000],[['wash083',8],666],[['wash084',2],55],[['wash085',1],11],[['wash088',8],666],[['wash089',2],55],[['wash090',1],11],[['wash091',11],1333],[['wash092',10],1000],[['wash093',8],666],[['wash094',2],55],[['wash095',1],11],[['wash096',11],1333],[['wash097',10],1000],[['wash098',8],666],[['wash099',2],55],[['wash100',1],11],[['wash101',11],1333],[['wash102',10],1000],[['wash103',8],666],[['wash104',2],55],[['wash105',1],11],[['wash106',11],1333],[['wash107',10],1000],[['wash108',8],666],[['wash109',2],55],[['wash110',1],11],[['wash111',11],1333],[['wash112',10],1000],[['wash113',8],666],[['wash114',2],55],[['wash115',1],11],[['wash118',8],666],[['wash119',2],55],[['wash120',1],11],[['wash121',11],1333],[['wash122',10],1000],[['wash123',8],666],[['wash124',2],55],[['wash125',1],11],[['wash126',11],1333],[['wash127',10],1000],[['wash128',8],666],[['wash129',2],55],[['wash130',1],11],[['wash131',11],1333],[['wash132',10],1000],[['wash133',8],666],[['wash134',2],55],[['wash135',1],11],[['wash136',11],1333],[['wash137',10],1000],[['wash138',8],666],[['wash139',2],55],[['wash140',1],11],[['wash141',11],1333],[['wash142',10],1000],[['wash143',8],666],[['wash144',2],55],[['wash145',1],11],[['wash148',8],666],[['wash149',2],55],[['wash150',1],11],[['wash151',11],1333],[['wash152',10],1000],[['wash153',8],666],[['wash154',2],55],[['wash155',1],11],[['wash156',11],1333],[['wash157',10],1000],[['wash158',8],666],[['wash159',2],55],[['wash160',1],11],[['wash161',11],1333],[['wash162',10],1000],[['wash163',8],666],[['wash164',2],55],[['wash165',1],11],[['wash166',11],1333],[['wash167',10],1000],[['wash168',8],666],[['wash169',2],55],[['wash170',1],11],[['wash171',11],1333],[['wash172',10],1000],[['wash173',8],666],[['wash174',2],55],[['wash175',1],11],[['wash176',11],1333],[['wash177',10],1000],[['wash178',8],666],[['wash179',2],55],[['wash180',1],11],[['wash181',11],1333],[['wash182',10],1000],[['wash183',8],666],[['wash184',2],55],[['wash185',1],11],[['wash186',11],1333],[['wash187',10],1000],[['wash188',8],666],[['wash189',2],55],[['wash190',1],11],[['wash191',11],1333],[['wash192',10],1000],[['wash193',8],666],[['wash194',2],55],[['wash195',1],11],[['wash196',11],1333],[['wash197',10],1000],[['wash198',8],666],[['wash199',2],55],[['wash200',1],11],[['wash201',11],1333],[['wash202',10],1000],[['wash203',8],666],[['wash204',2],55],[['wash205',1],11],[['wash206',11],1333],[['wash207',10],1000],[['wash208',8],666],[['wash209',2],55],[['wash210',1],11],[['wash211',11],1333],[['wash212',10],1000],[['wash213',8],666],[['wash214',2],55],[['wash215',1],11],[['wash216',11],1333],[['wash217',10],1000],[['wash218',8],666],[['wash219',2],55],[['wash220',1],11],[['wash221',11],1333],[['wash222',10],1000],[['wash223',8],666],[['wash224',2],55],[['wash225',1],11],[['wash226',11],1333],[['wash227',10],1000],[['wash228',8],666],[['wash229',2],55],[['wash230',1],11],[['wash231',11],1333],[['wash232',10],1000],[['wash233',8],666],[['wash234',2],55],[['wash235',1],11],[['wash236',11],1333],[['wash237',10],1000],[['wash238',8],666],[['wash239',2],55],[['wash240',1],11],[['wash241',11],1333],[['wash242',10],1000],[['wash243',8],666],[['wash244',2],55],[['wash245',1],11],[['wash246',11],1333],[['wash247',10],1000],[['wash248',8],666],[['wash249',2],55],[['wash250',1],11],[['wash251',11],1333],[['wash252',10],1000],[['wash253',8],666],[['wash254',2],55],[['wash255',1],11],[['wash256',11],1333],[['wash257',10],1000],[['wash258',8],666],[['wash259',2],55],[['wash260',1],11],[['wash261',11],1333],[['wash262',10],1000],[['wash263',8],666],[['wash264',2],55],[['wash265',1],11],[['wash266',11],1333],[['wash267',10],1000],[['wash268',8],666],[['wash269',2],55],[['wash270',1],11],[['wash271',11],1333],[['wash272',10],1000],[['wash273',8],666],[['wash274',2],55],[['wash275',1],11],[['wash276',11],1333],[['wash277',10],1000],[['wash278',8],666],[['wash279',2],55],[['wash280',1],11],[['wash281',11],1333],[['wash282',10],1000],[['wash283',8],666],[['wash284',2],55],[['wash285',1],11],[['wash286',11],1333],[['wash287',10],1000],[['wash288',8],666],[['wash289',2],55],[['wash290',1],11],[['wash291',11],1333],[['wash292',10],1000],[['wash293',8],666],[['wash294',2],55],[['wash295',1],11],]],



[100,[[['wash005',-100],200000],[['wash010',-100],200000],[['wash015',-100],200000],[['wash020',-100],200000],[['wash024',-100],2000000],[['wash025',-100],200000],[['wash029',-100],2000000],[['wash030',-100],200000],[['wash034',-100],2000000],[['wash035',-100],200000],[['wash039',-100],2000000],[['wash040',-100],200000],[['wash044',-100],2000000],[['wash045',-100],200000],[['wash049',-100],2000000],[['wash050',-100],200000],[['wash054',-100],2000000],[['wash055',-100],200000],[['wash059',-100],2000000],[['wash060',-100],200000],[['wash064',-100],2000000],[['wash065',-100],200000],[['wash069',-100],2000000],[['wash070',-100],200000],[['wash074',-100],2000000],[['wash075',-100],200000],[['wash079',-100],2000000],[['wash080',-100],200000],[['wash084',-100],2000000],[['wash085',-100],200000],[['wash089',-100],2000000],[['wash090',-100],200000],[['wash094',-100],2000000],[['wash095',-100],200000],[['wash099',-100],2000000],[['wash100',-100],200000],[['wash104',-100],2000000],[['wash105',-100],200000],[['wash109',-100],2000000],[['wash110',-100],200000],[['wash114',-100],2000000],[['wash115',-100],200000],[['wash119',-100],2000000],[['wash120',-100],200000],[['wash124',-100],2000000],[['wash125',-100],200000],[['wash129',-100],2000000],[['wash130',-100],200000],[['wash134',-100],2000000],[['wash135',-100],200000],[['wash139',-100],2000000],[['wash140',-100],200000],[['wash144',-100],2000000],[['wash145',-100],200000],[['wash149',-100],2000000],[['wash150',-100],200000],[['wash154',-100],2000000],[['wash155',-100],200000],[['wash159',-100],2000000],[['wash160',-100],200000],[['wash164',-100],2000000],[['wash165',-100],200000],[['wash169',-100],2000000],[['wash170',-100],200000],[['wash174',-100],2000000],[['wash175',-100],200000],[['wash179',-100],2000000],[['wash180',-100],200000],[['wash184',-100],2000000],[['wash185',-100],200000],[['wash189',-100],2000000],[['wash190',-100],200000],[['wash194',-100],2000000],[['wash195',-100],200000],[['wash199',-100],2000000],[['wash200',-100],200000],[['wash204',-100],2000000],[['wash205',-100],200000],[['wash209',-100],2000000],[['wash210',-100],200000],[['wash214',-100],2000000],[['wash215',-100],200000],[['wash219',-100],2000000],[['wash220',-100],200000],[['wash224',-100],2000000],[['wash225',-100],200000],[['wash229',-100],2000000],[['wash230',-100],200000],[['wash234',-100],2000000],[['wash235',-100],200000],[['wash239',-100],2000000],[['wash240',-100],200000],[['wash244',-100],2000000],[['wash245',-100],200000],[['wash249',-100],2000000],[['wash250',-100],200000],[['wash254',-100],2000000],[['wash255',-100],200000],[['wash259',-100],2000000],[['wash260',-100],200000],[['wash264',-100],2000000],[['wash265',-100],200000],[['wash269',-100],2000000],[['wash270',-100],200000],[['wash274',-100],2000000],[['wash275',-100],200000],[['wash279',-100],2000000],[['wash280',-100],200000],[['wash284',-100],2000000],[['wash285',-100],200000],[['wash289',-100],2000000],[['wash290',-100],200000],[['wash294',-100],2000000],[['wash295',-100],200000],]],

		],

        'equip_wash_cost':[['item406',[10,20,40,80,120]],['item409',[10,20,40,80,120]],['item408',[10,20,40,80,120]],['item407',[10,20,40,80,120]],['item410',[10,20,40,80,120]]],  
         #洗练消耗的材料道具id，[洗练费用，锁1的费用，锁2的费用，锁3的费用，锁4的费用]
        'equip_gold_finger':'item084',   
'equip_Reset_box':{'wash005':100,'wash010':100,'wash015':100,'wash020':100,'wash025':100,'wash030':100,'wash035':100,'wash040':100,'wash045':100,'wash050':100,'wash055':100,'wash060':100,'wash065':100,'wash070':100,'wash075':100,'wash080':100,'wash085':100,'wash090':100,'wash095':100,'wash100':100,'wash105':100,'wash110':100,'wash115':100,'wash120':100,'wash125':100,'wash130':100,'wash135':100,'wash140':100,'wash145':100,'wash150':100,'wash155':100,'wash160':100,'wash165':100,'wash170':100,'wash175':100,'wash180':100,'wash185':100,'wash190':100,'wash195':100,'wash200':100,'wash205':100,'wash210':100,'wash215':100,'wash220':100,'wash225':100,'wash230':100,'wash235':100,'wash240':100,'wash245':100,'wash250':100,'wash255':100,'wash260':100,'wash265':100,'wash270':100,'wash275':100,'wash280':100,'wash285':100,'wash290':100,'wash295':100,},



 'equip_make_normal':['item401','item402','item403','item404','item405',], #[4,[]],珍宝阁等级限制，令符列表,制作一次消耗的资源


 'equip_enhance_cost':[   #需要的材料对应[神兵,坐骑,奇珍,宝典,令符],需要的数量，成功率，额外成功率，额外成功率最多的叠加次数

['item452','item456','item464','item460','item468',1,0.9,0.1,1,],
['item452','item456','item464','item460','item468',2,0.7,0.1,3,],
['item452','item456','item464','item460','item468',4,0.5,0.1,5,],
['item452','item456','item464','item460','item468',6,0.3,0.1,7,],
['item452','item456','item464','item460','item468',8,0.2,0.08,16,],
['item452','item456','item464','item460','item468',10,0.1,0.06,18,],
['item452','item456','item464','item460','item468',12,0.08,0.02,37,],
['item452','item456','item464','item460','item468',15,0.05,0.02,38,],
['item453','item457','item465','item461','item469',4,0.2,0.05,16,],
['item453','item457','item465','item461','item469',6,0.12,0.05,18,],
['item453','item457','item465','item461','item469',8,0.08,0.02,46,],
['item453','item457','item465','item461','item469',10,0.06,0.02,47,],
['item454','item458','item466','item462','item470',2,0.15,0.03,30,],
['item454','item458','item466','item462','item470',4,0.1,0.03,30,],
['item454','item458','item466','item462','item470',6,0.08,0.02,46,],
['item454','item458','item466','item462','item470',8,0.06,0.02,47,],
['item455','item459','item467','item463','item471',1,0.1,0.02,30,],
['item455','item459','item467','item463','item471',2,0.08,0.01,30,],
['item455','item459','item467','item463','item471',3,0.06,0.01,46,],
['item455','item459','item467','item463','item471',4,0.05,0.01,94,],
['item455','item459','item467','item463','item471',4,0.1,0.02,100,],
['item455','item459','item467','item463','item471',6,0.08,0.02,100,],
['item455','item459','item467','item463','item471',8,0.06,0.02,100,],
['item455','item459','item467','item463','item471',10,0.05,0.02,100,],










],


   "equip_enhance_level":[['merge',0,20],['science','133',1]],#当前的强化等级上限，将当前数组符合条件的全部相加等于当前强化等级上限，merge_x表示合服次数的等级，science_X表示玩家对应科技的等级

 'equip_enhance_show':[8,12,16,20],  #3条额外属性可以看到的强化等级
 'equip_enhance_times':[150,10],  #当道具数量>500时，强化10次





   #金手指重置库

       
##################################
###########星辰杂项#################
###################################



##################################

      'star_exp':{   #【星辰升级经验对应exp_type】【可用的经验书】【1及分解道具】
         '0':[[50,100,150,200,250,300,350,400,450,500,550,600,650,700,750,800,850,900,950,1000,1050,1100,1150,1200,1250,1300,1350,1400,1450,1500,1600,1700,1800,1900,2000,2100,2200,2300,2400,],{'item604':10, 'item605':100, 'item606':500,},'item605'],
         '1':[[250,500,1000,1500,2000,3000,4000,6000,8000,6000,7500,9000,11000,14000,],{'item601':10, 'item602':100, 'item603':500,},'item602'],
         '2':[[1500,2500,4000,6000,8000,6000,7500,9000,11000,14000,],{'item601':10, 'item602':100, 'item603':500,},'item602'],
         '3':[[100,150,200,250,300,350,400,450,500,600,700,800,900,1000,1100,1200,1300,1400,1500,1650,1800,1950,2100,2250,2400,2550,2700,2850,3000,3200,3400,3600,3800,4000,4200,4400,4600,4800,5000],{'item604':10, 'item605':100, 'item606':500,},'item605'],
     },

   'star_lv_science':[['130',2],['127',2],['129',2],['128',2],['132',1],['132',1],['132',1],['132',1],],#科技提升星辰等级上限，对应星辰type

      'fix_position':[4,0,1,2,3,5,0,1,2,3,6,0,1,2,3,7,0,1,2,3], #英雄身上的星辰栏
      'fix_star_only':[     #单例的星辰，同个英雄只能装备1个
         'star01','star02','star03','star04','star16','star17','star18','star19','star20','star21',     #金色
         'star50','star51','star52','star53',     #红色攻击
         'star60','star61','star62','star63',     #紫色兵力
         'star70','star71','star72','star73',     #绿色防御
      ], 
                     
       'star_price':[50,450,1000,10000,999,'item075'],
#[钻石单抽价格],[钻石10抽价格],[单抽赠送金币],[5抽赠送金币],[最大限购次数],[代币道具]
       'star_first_gift':'star0501',

	'star_box_0':[              #【幸运区间】【box库权重

[99,[[['star0101',0],21],[['star0201',0],21],[['star0501',0],100],[['star0601',0],100],[['star0701',0],100],[['star0801',0],5],[['star1001',0],5],[['star1201',0],5],[['star1401',0],5],[['item604',12],10000],[['item605',12],5000],[['item601',12],3750],[['item602',12],1000],]],





[100,[[['star0801',-100],857],[['star1001',-100],857],[['star1201',-100],857],[['star1401',-100],857],[['item606',-100],2142],[['item603',-100],1578],[['star0101',-100],3000],[['star0201',-100],3000],[['star0501',-100],10000],[['star0601',-100],10000],[['star0701',-100],10000],]],







],


	'star_box_1':[              #【幸运区间】【box库权重



[99,[[['star0101',0],21],[['star0201',0],21],[['star0501',0],100],[['star0601',0],100],[['star0701',0],100],[['star0801',0],5],[['star1001',0],5],[['star1201',0],5],[['star1401',0],5],[['item604',12],10000],[['item605',12],5000],[['item601',12],3750],[['item602',12],1000],[['star1601',0],21],[['star1701',0],21],]],







[100,[[['star0801',-100],857],[['star1001',-100],857],[['star1201',-100],857],[['star1401',-100],857],[['item606',-100],2142],[['item603',-100],1578],[['star0101',-100],3000],[['star0201',-100],3000],[['star0501',-100],10000],[['star0601',-100],10000],[['star0701',-100],10000],[['star1601',-100],3000],[['star1701',-100],3000],]],





],

'show_chance_0':[
['star0101',0.008],['star0201',0.008],['star0501',0.03],['star0601',0.03],['star0701',0.03],['star0801',0.002],['star1001',0.002],['star1201',0.002],['star1401',0.002],['item601',0.17],['item602',0.04],['item603',0.004],['item604',0.445],['item605',0.222],['item606',0.005],



		], 

'star_default':[['item601','item602','item603'],['item604','item605','item606']], 

	'star_box_2':[              #【幸运区间】【box库权重

[99,[[['star0801',0],3],[['star1001',0],3],[['star1201',0],3],[['star1401',0],3],[['star0901',0],3],[['star1101',0],3],[['star1301',0],3],[['star1501',0],3],[['star0101',0],15],[['star0201',0],15],[['star1601',0],15],[['star1701',0],15],[['star1801',0],15],[['star1901',0],15],[['star2001',0],15],[['star2101',0],15],[['star0501',0],100],[['star0601',0],100],[['star0701',0],100],[['item602',10],1000],[['item601',10],3750],[['item605',10],5000],[['item604',10],10000],]],








[100,[[['star0801',-100],600],[['star1001',-100],600],[['star1201',-100],600],[['star1401',-100],600],[['item606',-100],2142],[['item603',-100],1578],[['star0101',-100],2068],[['star0201',-100],2068],[['star0501',-100],10000],[['star0601',-100],10000],[['star0701',-100],10000],[['star1601',-100],2068],[['star1701',-100],2068],[['star1801',-100],2068],[['star1901',-100],2068],[['star2001',-100],2068],[['star2101',-100],2068],[['star0901',-100],600],[['star1101',-100],600],[['star1301',-100],600],[['star1501',-100],600],]],











],

	'star_box_3':[              #【幸运区间】【box库权重

[99,[[['star0801',0],3],[['star1001',0],3],[['star1201',0],3],[['star1401',0],3],[['star0901',0],3],[['star1101',0],3],[['star1301',0],3],[['star1501',0],3],[['star0101',0],15],[['star0201',0],15],[['star1601',0],15],[['star1701',0],15],[['star1801',0],15],[['star1901',0],15],[['star2001',0],15],[['star2101',0],15],[['star0501',0],100],[['star0601',0],100],[['star0701',0],100],[['item602',10],1000],[['item601',10],3750],[['item605',10],5000],[['item604',10],10000],]],








[100,[[['star0801',-100],600],[['star1001',-100],600],[['star1201',-100],600],[['star1401',-100],600],[['item606',-100],2142],[['item603',-100],1578],[['star0101',-100],2068],[['star0201',-100],2068],[['star0501',-100],10000],[['star0601',-100],10000],[['star0701',-100],10000],[['star1601',-100],2068],[['star1701',-100],2068],[['star1801',-100],2068],[['star1901',-100],2068],[['star2001',-100],2068],[['star2101',-100],2068],[['star0901',-100],600],[['star1101',-100],600],[['star1301',-100],600],[['star1501',-100],600],]],











],


	'star_box_4':[              #【幸运区间】【box库权重

[99,[[['star0801',0],5],[['star1001',0],5],[['star1201',0],5],[['star1401',0],5],[['star0901',0],5],[['star1101',0],5],[['star1301',0],5],[['star1501',0],5],[['star0101',0],15],[['star0201',0],15],[['star1601',0],15],[['star1701',0],15],[['star1801',0],15],[['star1901',0],15],[['star2001',0],15],[['star2101',0],15],[['star0301',0],15],[['star0401',0],15],[['star0501',0],100],[['star0601',0],100],[['star0701',0],100],[['item602',10],1000],[['item601',10],3750],[['item605',10],5000],[['item604',10],10000],[['item606',0],0],[['item603',0],0],]],







[100,[[['star0801',-100],750],[['star1001',-100],750],[['star1201',-100],750],[['star1401',-100],750],[['item606',-100],2142],[['item603',-100],1578],[['star0101',-100],2068],[['star0201',-100],2068],[['star0501',-100],10000],[['star0601',-100],10000],[['star0701',-100],10000],[['star1601',-100],2068],[['star1701',-100],2068],[['star1801',-100],2068],[['star1901',-100],2068],[['star2001',-100],2068],[['star2101',-100],2068],[['star0301',-100],2068],[['star0401',-100],2068],[['star0901',-100],750],[['star1101',-100],750],[['star1301',-100],750],[['star1501',-100],750],[['star5001',-100],75],[['star5101',-100],75],[['star5201',-100],75],[['star5301',-100],75],[['star6001',-100],75],[['star6101',-100],75],[['star6201',-100],75],[['star6301',-100],75],[['star7001',-100],75],[['star7101',-100],75],[['star7201',-100],75],[['star7301',-100],75],]],











],
	'star_box_5':[              #【幸运区间】【box库权重

[99,[[['star0801',0],5],[['star1001',0],5],[['star1201',0],5],[['star1401',0],5],[['star0901',0],5],[['star1101',0],5],[['star1301',0],5],[['star1501',0],5],[['star0101',0],15],[['star0201',0],15],[['star1601',0],15],[['star1701',0],15],[['star1801',0],15],[['star1901',0],15],[['star2001',0],15],[['star2101',0],15],[['star0301',0],15],[['star0401',0],15],[['star0501',0],100],[['star0601',0],100],[['star0701',0],100],[['item602',10],1000],[['item601',10],3750],[['item605',10],5000],[['item604',10],10000],[['item606',0],0],[['item603',0],0],]],







[100,[[['star0801',-100],750],[['star1001',-100],750],[['star1201',-100],750],[['star1401',-100],750],[['item606',-100],2142],[['item603',-100],1578],[['star0101',-100],2068],[['star0201',-100],2068],[['star0501',-100],10000],[['star0601',-100],10000],[['star0701',-100],10000],[['star1601',-100],2068],[['star1701',-100],2068],[['star1801',-100],2068],[['star1901',-100],2068],[['star2001',-100],2068],[['star2101',-100],2068],[['star0301',-100],2068],[['star0401',-100],2068],[['star0901',-100],750],[['star1101',-100],750],[['star1301',-100],750],[['star1501',-100],750],[['star5001',-100],75],[['star5101',-100],75],[['star5201',-100],75],[['star5301',-100],75],[['star6001',-100],75],[['star6101',-100],75],[['star6201',-100],75],[['star6301',-100],75],[['star7001',-100],75],[['star7101',-100],75],[['star7201',-100],75],[['star7301',-100],75],]],











],

	'star_box_6':[              #【幸运区间】【box库权重

[99,[[['star0801',0],5],[['star1001',0],5],[['star1201',0],5],[['star1401',0],5],[['star0901',0],5],[['star1101',0],5],[['star1301',0],5],[['star1501',0],5],[['star0101',0],15],[['star0201',0],15],[['star1601',0],15],[['star1701',0],15],[['star1801',0],15],[['star1901',0],15],[['star2001',0],15],[['star2101',0],15],[['star0301',0],15],[['star0401',0],15],[['star0501',0],100],[['star0601',0],100],[['star0701',0],100],[['item602',10],1000],[['item601',10],3750],[['item605',10],5000],[['item604',10],10000],[['item606',0],0],[['item603',0],0],]],







[100,[[['star0801',-100],750],[['star1001',-100],750],[['star1201',-100],750],[['star1401',-100],750],[['item606',-100],2142],[['item603',-100],1578],[['star0101',-100],2068],[['star0201',-100],2068],[['star0501',-100],10000],[['star0601',-100],10000],[['star0701',-100],10000],[['star1601',-100],2068],[['star1701',-100],2068],[['star1801',-100],2068],[['star1901',-100],2068],[['star2001',-100],2068],[['star2101',-100],2068],[['star0301',-100],2068],[['star0401',-100],2068],[['star0901',-100],750],[['star1101',-100],750],[['star1301',-100],750],[['star1501',-100],750],[['star5001',-100],75],[['star5101',-100],75],[['star5201',-100],75],[['star5301',-100],75],[['star6001',-100],75],[['star6101',-100],75],[['star6201',-100],75],[['star6301',-100],75],[['star7001',-100],75],[['star7101',-100],75],[['star7201',-100],75],[['star7301',-100],75],]],











],

	'star_box_7':[              #【幸运区间】【box库权重

[99,[[['star0801',0],5],[['star1001',0],5],[['star1201',0],5],[['star1401',0],5],[['star0901',0],5],[['star1101',0],5],[['star1301',0],5],[['star1501',0],5],[['star0101',0],15],[['star0201',0],15],[['star1601',0],15],[['star1701',0],15],[['star1801',0],15],[['star1901',0],15],[['star2001',0],15],[['star2101',0],15],[['star0301',0],15],[['star0401',0],15],[['star0501',0],100],[['star0601',0],100],[['star0701',0],100],[['item602',10],1000],[['item601',10],3750],[['item605',10],5000],[['item604',10],10000],[['item606',0],0],[['item603',0],0],]],







[100,[[['star0801',-100],750],[['star1001',-100],750],[['star1201',-100],750],[['star1401',-100],750],[['item606',-100],2142],[['item603',-100],1578],[['star0101',-100],2068],[['star0201',-100],2068],[['star0501',-100],10000],[['star0601',-100],10000],[['star0701',-100],10000],[['star1601',-100],2068],[['star1701',-100],2068],[['star1801',-100],2068],[['star1901',-100],2068],[['star2001',-100],2068],[['star2101',-100],2068],[['star0301',-100],2068],[['star0401',-100],2068],[['star0901',-100],750],[['star1101',-100],750],[['star1301',-100],750],[['star1501',-100],750],[['star5001',-100],75],[['star5101',-100],75],[['star5201',-100],75],[['star5301',-100],75],[['star6001',-100],75],[['star6101',-100],75],[['star6201',-100],75],[['star6301',-100],75],[['star7001',-100],75],[['star7101',-100],75],[['star7201',-100],75],[['star7301',-100],75],]],











],



'show_chance_1':[
['star0101',0.007],['star0201',0.007],['star1601',0.007],['star1701',0.007],['star0501',0.026],['star0601',0.026],['star0701',0.026],['star0801',0.002],['star1001',0.002],['star1201',0.002],['star1401',0.002],['item601',0.17],['item602',0.04],['item603',0.004],['item604',0.445],['item605',0.222],['item606',0.005],



		], 


'show_chance_2':[
['star0801',0.0014],['star1001',0.0014],['star1201',0.0014],['star1401',0.0014],['star0901',0.0014],['star1101',0.0014],['star1301',0.0014],['star1501',0.0014],['star0101',0.004],['star0201',0.004],['star1601',0.004],['star1701',0.004],['star1801',0.004],['star1901',0.004],['star2001',0.004],['star2101',0.004],['star0501',0.02],['star0601',0.02],['star0701',0.02],['item602',0.046],['item601',0.17],['item605',0.2247],['item604',0.45],['item606',0.0034],['item603',0.0027],




		], 

'show_chance_3':[
['star0801',0.0014],['star1001',0.0014],['star1201',0.0014],['star1401',0.0014],['star0901',0.0014],['star1101',0.0014],['star1301',0.0014],['star1501',0.0014],['star0101',0.004],['star0201',0.004],['star1601',0.004],['star1701',0.004],['star1801',0.004],['star1901',0.004],['star2001',0.004],['star2101',0.004],['star0501',0.02],['star0601',0.02],['star0701',0.02],['item602',0.046],['item601',0.17],['item605',0.2247],['item604',0.45],['item606',0.0034],['item603',0.0027],





		], 

'show_chance_4':[
['star0801',0.0014],['star1001',0.0014],['star1201',0.0014],['star1401',0.0014],['star0901',0.0014],['star1101',0.0014],['star1301',0.0014],['star1501',0.0014],['star0101',0.004],['star0201',0.004],['star1601',0.004],['star1701',0.004],['star1801',0.004],['star1901',0.004],['star2001',0.004],['star2101',0.004],['star0501',0.02],['star0601',0.02],['star0701',0.02],['item601',0.161],['item602',0.045],['item603',0.003],['item604',0.45],['item605',0.23],['item606',0.0038],['star0301',0.0014],['star0401',0.0014],['star5001',0.0001],['star5101',0.0001],['star5201',0.0001],['star5301',0.0001],['star6001',0.0001],['star6101',0.0001],['star6201',0.0001],['star6301',0.0001],['star7001',0.0001],['star7101',0.0001],['star7201',0.0001],['star7301',0.0001],
],

'show_chance_5':[
['star0801',0.0014],['star1001',0.0014],['star1201',0.0014],['star1401',0.0014],['star0901',0.0014],['star1101',0.0014],['star1301',0.0014],['star1501',0.0014],['star0101',0.004],['star0201',0.004],['star1601',0.004],['star1701',0.004],['star1801',0.004],['star1901',0.004],['star2001',0.004],['star2101',0.004],['star0501',0.02],['star0601',0.02],['star0701',0.02],['item601',0.161],['item602',0.045],['item603',0.003],['item604',0.45],['item605',0.23],['item606',0.0038],['star0301',0.0014],['star0401',0.0014],['star5001',0.0001],['star5101',0.0001],['star5201',0.0001],['star5301',0.0001],['star6001',0.0001],['star6101',0.0001],['star6201',0.0001],['star6301',0.0001],['star7001',0.0001],['star7101',0.0001],['star7201',0.0001],['star7301',0.0001],




		], 

'show_chance_6':[
['star0801',0.0014],['star1001',0.0014],['star1201',0.0014],['star1401',0.0014],['star0901',0.0014],['star1101',0.0014],['star1301',0.0014],['star1501',0.0014],['star0101',0.004],['star0201',0.004],['star1601',0.004],['star1701',0.004],['star1801',0.004],['star1901',0.004],['star2001',0.004],['star2101',0.004],['star0501',0.02],['star0601',0.02],['star0701',0.02],['item601',0.161],['item602',0.045],['item603',0.003],['item604',0.45],['item605',0.23],['item606',0.0038],['star0301',0.0014],['star0401',0.0014],['star5001',0.0001],['star5101',0.0001],['star5201',0.0001],['star5301',0.0001],['star6001',0.0001],['star6101',0.0001],['star6201',0.0001],['star6301',0.0001],['star7001',0.0001],['star7101',0.0001],['star7201',0.0001],['star7301',0.0001],




		], 

'show_chance_7':[
['star0801',0.0014],['star1001',0.0014],['star1201',0.0014],['star1401',0.0014],['star0901',0.0014],['star1101',0.0014],['star1301',0.0014],['star1501',0.0014],['star0101',0.004],['star0201',0.004],['star1601',0.004],['star1701',0.004],['star1801',0.004],['star1901',0.004],['star2001',0.004],['star2101',0.004],['star0501',0.02],['star0601',0.02],['star0701',0.02],['item601',0.161],['item602',0.045],['item603',0.003],['item604',0.45],['item605',0.23],['item606',0.0038],['star0301',0.0014],['star0401',0.0014],['star5001',0.0001],['star5101',0.0001],['star5201',0.0001],['star5301',0.0001],['star6001',0.0001],['star6101',0.0001],['star6201',0.0001],['star6301',0.0001],['star7001',0.0001],['star7101',0.0001],['star7201',0.0001],['star7301',0.0001],




		],


###########问道杂项#################
###################################
	'resolve_cost':{
		'0':[800,30,2],    #rarity为0的英雄（良才），[消耗银币，获得将魂，技能随机次数]
		'1':[1600,60,4],   #rarity为1的英雄（名将）
		'2':[1600,60,4],   #rarity为2的英雄（国士）
		'3':[1600,60,4],   #rarity为3的英雄（巾帼）
		'4':[3200,60,4],   #rarity为4的英雄（传奇）
		},
	'building_soul':['building006',0,0.1],   #相关建筑，初始化增幅，每升一级增幅
	'building_army_go':['building003',0,0.01],   #军府加速，初始化增幅，每升一级增幅
###########编队杂项#################
###################################
	'troop_que':2,        #初始化编队上限

###########名将切磋杂项#################
###################################
	'catch_hero_init': [['hero701',['144','62','350']],['hero702',['146','31','353']],['hero737',['148','66','355']]],
	'hero_range':[['hero701',1500],['hero702',1500],['hero703',1500],['hero704',1500],['hero705',1500],['hero706',1500],['hero708',1500],['hero710',1500],['hero712',1500],['hero715',1500],['hero716',1500],['hero717',1500],['hero719',1500],
['hero722',1500],['hero725',1000],['hero726',1000],['hero727',1000],['hero728',1000],['hero729',1000],['hero730',1000],['hero731',1000],['hero732',1000],['hero733',1000],['hero734',1000],['hero735',1000],['hero736',1000],['hero737',1000],['hero738',1000],['hero739',1000],['hero740',1000],['hero741',1000],['hero742',1000],['hero743',1000],['hero744',1000],['hero745',1000],['hero746',1000],['hero747',1000],['hero748',1000],['hero749',1000],['hero750',1000],['hero751',1000],['hero752',1000],['hero753',1000],['hero754',1000],['hero755',1000],['hero756',1000],['hero757',1000],['hero758',1000],['hero759',1000],['hero760',1000],
],
        'catch_dead_speak':'catch_dead_speak',
        'catch_enemy_power':1.5,        #切磋推荐战力系数
	'catch_hero':[10,3,5,60,3,10,5,10],        #初始化所有英雄等级，每打一次英雄成长等级下限，等级上限，最终等级上限,能同时存在的名将个数,锁定英雄花费coin,免费刷新次数，刷新英雄花费coin
	'catch_buy_count':[50,100,150,200,250],   #购买价格（coin），数组长度为购买上限
	'catch_intial_num': 5,   #名将切磋初始送n次
	'catch_award':{
		'0':[4,1],    #rarity为0的英雄（良才），[英雄碎片，能够获得技能种类个数]
		'1':[3,2],   #rarity为1的英雄（名将）
		'2':[2,2],   #rarity为2的英雄（国士）
		'3':[2,2],   #rarity为3的英雄（巾帼）
		},
	'catch_hero_base':'base',            #机器人库
	'init_city':{
		'0':['142','144','146','148','159','160','161'],    #魏国初始城池范围
		'1':[ '26', '31', '59', '62', '65', '66', '70'],    #蜀国初始城池范围
		'2':['348','350','353','355','358','364','370'],    #吴国初始城池范围
	},
###########战力杂项#################
###################################
        #玩家官邸达到8\15\19\25\28\31级后，将统计最强3\4\5\6\7\8名英雄，作为最强战力
	'power_herocount':[[7,2],[14,3],[18,4],[24,5],[27,6],[30,7],[33,8]],        #对应官邸等级计算的英雄战力个数(小于等于[0]取[1])

###########随机技能池#################
###################################

		'heroskill':{'skill225':10,'skill226':10,'skill227':10,'skill228':10,'skill229':10,'skill230':10,'skill231':10,'skill232':10,'skill236':10},  #英雄技能选取限制
		'armyskill0':{'skill201':10,'skill289':10,'skill202':10,'skill204':10,'skill203':10},  #步兵技能选取限制
		'armyskill1':{'skill207':10,'skill209':10,'skill210':10,'skill208':10,'skill290':10},  #骑兵技能选取限制
		'armyskill2':{'skill213':10,'skill215':10,'skill214':10,'skill216':10,'skill217':10},  #弓兵技能选取限制
		'armyskill3':{'skill219':10,'skill220':10,'skill221':10,'skill222':10,'skill223':10},  #方士技能选取限制
		'helplimit':{'skill260':10,'skill261':10,'skill266':10,'skill267':10,'skill268':10,'skill269':10,'skill270':10,},  #辅助技能选取限制

###########随机英雄池#################
###################################开服时以此随机群雄逐鹿机器人hid，修改后如要生效需要后台重刷
	'herorange_base':{
		'hero701':10,'hero702':10,'hero703':10,'hero704':10,'hero705':10,'hero706':10,'hero708':10,'hero710':10,'hero712':10,'hero715':10,'hero716':10,'hero717':10,'hero719':10,'hero722':10,'hero725':10,'hero726':10,'hero727':10,'hero728':10,'hero729':10,'hero730':10,'hero731':10,'hero732':10,'hero733':10,'hero734':10,'hero735':10,'hero736':10,'hero737':10,'hero738':10,'hero739':10,'hero740':10,'hero760':10,'hero761':10,
		},

###########聊天杂项#################
###################################
	'worldtalk_spend':[0], #世界聊天花费，最后一位循环，为0时显示免费，数组长度为1且数字为0则显示“发送”不显示元宝
	'init_channel':1,        #默认进入的聊天频道
	'blacklist_limit':50,            #黑名单人数上限
	'speak_cd':[2,2,2],         #[世界, 国家，国家栋梁]三个频道的说话间隔（秒）
        'cache_time': 10,      #分钟
        'cache_num': [20, 10, 10],    #世界，国家，国家栋梁
	'system_massage':{
		'hero_star':{                     #英雄升星
			'1':{
				'need':[12],          #英雄星级达到12星
				'info':'speak_hero_star1',
				'icon':0,							#0世界   1国家   2军团
			},
			'2':{
				'need':[18],          #英雄星级达到18星
				'info':'speak_hero_star2',
				'icon':0,
			},
		},
		'hero_skill':{            #英雄升技能
			'1':{
				'need':[[0,1,2,3,4],13,13],          #[[技能type],大于等于该等级,小于等于该等级]
				'info':'hero_skillup1',
				'icon':0,
			},	
			'2':{
				'need':[[0,1,2,3,4],20,25],          #[[技能type],大于等于该等级,小于等于该等级]
				'info':'hero_skillup2',
				'icon':0,
			},	
			'3':{
				'need':[[0,1,2,3,4],26,30],          #[[技能type],大于等于该等级,小于等于该等级]
				'info':'hero_skillup3',
				'icon':0,
			},
		},
		'look_star':{						#观星获得神纹
			'1':{
				'need':[4,5,6,7],   #如果观星得到的是神纹
				'info':'look_star1',
				'icon':0
			}
		},	
		'guild_atkcity':{						#军团成员攻城
			'1':{
				'need':[3],   #玩家所属军团成员在占领某城池时，战功获得前3名
				'info':'guild_atkcity',
				'icon':2
			}
		},
		'guild_pve':{					#军团异邦来访
			'1':{
				'info':'guild_pve',
				'icon':2
			},
		},
		'guild_corps':{					#军团中有人成为太守
			'1':{
				'info':'guild_corps',
				'icon':2
			},
		},
		'cityfight_victory':{ #攻城胜利
			'1':{
				'need':[0,5],   #大于0，小于等于5
				'info':'cityfight_victory1',
				'icon':0
			},
			'2':{
				'need':[5,30],   #大于5，小于等于30
				'info':'cityfight_victory2',
				'icon':0
			},
			'3':{
				'need':[30,9999],   #大于5，小于等于9999
				'info':'cityfight_victory3',
				'icon':0
			},
		},
		'cityfight_def_victory':{ #防守胜利
			'1':{
				'info':'cityfight_def_victory',
				'icon':0
			},
		},
		'limit_free':{ #限时免单免单成功
			'1':{
				'info':'free_show_speak',
				'icon':0
			},
		},
		'pk_user_first':{#夺得第一名发聊天推送
			'1':{
				'info':'pk_talkshow',
				'icon':0
			},
		},   


                #建造推送  斥候急报！{0}（国家）组建了新的{1}（攻城器械的名字），已经赶赴战场 
		
               'build_ballista':{#攻城器械建造
			'1':{
				'info':'500028',
				'icon':0
			},
		},  

                #击杀推送  {0}（国家）{1}（官职）{2}（玩家）摧毁了一台{3}（国家）的{4}（攻城器械的名字）
               'kill_ballista':{#攻城器械被消灭
			'1':{
				'info':'500029',
				'icon':0
			},
		},  


  
               'pk_yard':{#比武大会
			'1':{
                                'need':['1',[18,00]],   #[季节，时间]
				'info':'pk_yard1',#每个夏天的18:00准时推送
				'icon':0
			},
                        '2':{
                                'need':['1',[22,00]],   #[季节，时间]
				'info':'pk_yard2',#每个夏天的22:00准时推送
				'icon':0
			},
                        '3':{
				'info':'pk_yard3',#比武大会开始时准时推送,比武大会begin_time，数组第二个值开始时触发
				'icon':0
			},
                        '4':{
				'info':'pk_yard4',#比武大会八强可以押注时准时推送，combat_time，第六轮，数据第三场结束时触发
				'icon':0
			},
                        '5':{
				'info':'pk_yard5',#比武大会决赛时准时推送，combat_time，第七轮，数据第二个场结束时触发
				'icon':0
			},
                        '6':{
				'info':'pk_yard6',#比武大会决出冠军时准时推送，combat_time，第七轮，数据第三场结果时触发
				'icon':0
			},
		},

		'auction_end':{		#拍卖结束消息
			'1':{
                                'need':[-1],
				'info':'550043',	#文本无带入内容
				'icon':0,
			},
			'2':{
                                'need':[-1],
				'info':'550044',	#文本带入，0=礼包名称，1=当前价格，2=落锤时间（当前时间+300秒）
				'icon':0,
			},
			'3':{		#单个商品竞拍完成
				'info':'550034',	#文本带入，0=得主名称，1=礼包名称
				'icon':0,
			},
		},  
               'equip_box':{#轩辕铸宝
			'1':{
				'info':'equip_box_msg', # 文本代入 0=玩家名  1=宝物名字  
				'icon':0
			 },
		},
               'equip_enhance':{ #宝物强化  返回['玩家名字','宝物id','宝物强化等级']
			'1':{
				'need':[8],          #宝物强化到8级
				'info':'speak_equip_enhance1',
				'icon':0,							
			},
			'2':{
				'need':[12],          #宝物强化到12级
				'info':'speak_equip_enhance2',
				'icon':0,
			},
			'3':{
				'need':[16],          #宝物强化到16级
				'info':'speak_equip_enhance3',
				'icon':0,
			},
			'4':{
				'need':[20],          #宝物强化到20级
				'info':'speak_equip_enhance4',
				'icon':0,
			},
			'5':{
				'need':[24],          #宝物强化到24级
				'info':'speak_equip_enhance5',
				'icon':0,
			},
		},       

###############擂台赛聊天推送#########
		'arena_start':{		#竞猜开始，无带入内容
			'0':{
				'need':[-1],
				'info':'500107',
				'icon':0,
			},
		},
		'arena_attack':{		#攻擂开始，无带入内容
			'0':{
				'need':[-1],
				'info':'500108',
				'icon':0,
			},
		},
		'arena_newmaster':{		#任意擂台变更擂主，{0}{1}成为了{2}的新擂主。[country,uname,arena_type]
			'1':{		#{0}带入 国家名+玩家名，{1}带入擂台名
				'info':'500109',
				'icon':0,
			},
		},
		'arena_cha_add':{		#任意擂台的buff {1}的攻擂加成已经提高到{2}
			'1':{		#{1}带入擂台名，{2}带入need
				'need':[0.5],
				'info':'500110',
				'icon':0,
			},
			'2':{
				'need':[0.6],
				'info':'500110',
				'icon':0,
			},
		},
		'arena_pool_max':{		#任意擂台的奖池值，等于500时
			'1':{		#{1}带入擂台名
				'need':[200],
				'info':'500111',
				'icon':0,
			},
			'2':{		#{1}带入擂台名
				'need':[300],
				'info':'500111',
				'icon':0,
			},
			'3':{		#{1}带入擂台名
				'need':[400],
				'info':'500111',
				'icon':0,
			},
			'4':{		#{1}带入擂台名
				'need':[500],
				'info':'500111',
				'icon':0,
			},
			'5':{		#{1}带入擂台名
				'need':[700],
				'info':'500111',
				'icon':0,
			},
			'6':{		#{1}带入擂台名
				'need':[1000],
				'info':'500111',
				'icon':0,
			},
		},
		'arena_winner':{		#任意擂台产生最终擂主，{0}{1}成为本期擂台赛{2}的最终擂主。[country,uname,arena_type]
			'1':{		#{0}带入 国家名+玩家名，{1}带入擂台名
				'info':'500112',
				'icon':0,
			},
		},
		'arena_kill':{		#任意擂主连续击败X名挑战者，{x}擂主{y}已经连续挫败了5名挑战者[arena_type,country,uname]
			'1':{		#{0}带入 国家名+玩家名，{1}带入擂台名
				'need':[5],
				'info':'500113',
				'icon':0,
			},
			'2':{
				'need':[10],
				'info':'500114',
				'icon':0,
			},
		}, 
################跨服战相关##############################
                'duplicate_fight_victory':{ #进攻胜利
			'1':{
                                'need':[3,4], #地块名字
				'info':'500438',#{0}被谁{1}占领了
				'icon':3
			},
                        '2':{
                                'need':[5,7],
				'info':'500438',
				'icon':3
			},
		},
################主公觉醒##############################
		'big_shot':{		#
			'1':{		#
				'need':['awaken',15],		#只要是发觉醒奖励，并且当前抽奖次数小于等于15，就推送
				'info':'big_shot_10',		#{0}=玩家名称，{1}=当前抽奖次数，{2}=奖励内容()
				'icon':0,
			},
		},
################神灵拍卖##############################
		'sale_end':{
			'1':{
				'info':'gods_text049',		#竞标成功的全服推送，{0}带入玩家名称，{1}带入神灵名称
				'icon':0,
			}
		},
################伏魔战场##############################
		'exorcism_hit':{
			'1':{
				'info':'gods_text070',#伏魔战场伤害推送（本国）竞标成功的全服推送，{0}带入玩家名称，{1}带入boss名称，{2}带入伤害
				'icon':0,
			}
		},

                
	},


#################排行榜###############
'rank_refresh':1,    #排行榜每隔10分钟刷新一次；
'rank_shownum':500,    #排行榜后端数量(此数值要大于等于世界等级所取的数值，即大于等于public_lv的第三个值)；
'rank_showworld':[100,80],    #展示100个，要进前100才可切换按钮
'rank_worldlimit':{
	'building_lv':13,    #玩家等级大于等于
	'rank_country':[50,50,50],
},

	'rank_cover':{   		#排行榜，隐藏玩家，可缺省
		'merge':2,		#合服次数大于等于X
		'day':15,		#离线天数大于等于X
		'pf': [ 'ios_djcy','ios_mj1','ios_mjm1','ios_mjm2','ios_mjm5','ios', 'hw','hw_gzsgz','h5_37','37_ios','h5_twyx','h5_twyx2','h5_twyx3','h5_twyx4','h5_twyx5','h5_yyjh','h5_yyjh2','h5_7k','h5_leyou','h5_360','h5_muzhi','h5_muhi2','h5_1377','caohua','h5_ch','h5_ch2', 'test', 'test_zh', 'test_taptap', 'test_dx', 'test_uc','test_sogou1', 'test_sogou2', 'test_jrtt0', 'test_jrtt1', 'test_jrtt2', 'test_jrtt3', 'test_jrtt4', 'test_jrtt5', 'test_jrtt6', 'test_jrtt7', 'test_jrtt8', 'test_jrtt9','test_jrtt10', 'test_baidu1', 'test_baidu2', 'test_baidu3', 'test_baidu4', 'test_baidu5', 'test_tt1', 'test_tt2', 'test_tt3', 'test_tt4', 'test_tt5', 'test_tt6', 'test_tt7', 'test_tt8', 'test_tt9', 'test_tt10', 'test_tt11', 'test_tt12', 'test_tt13', 'test_tt14', 'test_tt15', 'test_tt16', 'test_tt17', 'test_tt18', 'test_tt19', 'test_tt20', 'test_tt21', 'test_tt22', 'test_tt23', 'test_tt24', 'test_tt25', 'test_tt26', 'test_tt27', 'test_tt28', 'test_tt29', 'test_tt30', 'test_tt31', 'test_tt32', 'test_tt33', 'test_tt34', 'test_tt35', 'test_tt36', 'test_tt37', 'test_tt38', 'test_tt39', 'test_tt40', 'test_tt41', 'test_tt42', 'test_tt43', 'test_tt44', 'test_tt45', 'test_tt46', 'test_tt47', 'test_tt48', 'test_tt49', 'test_tt50', 'test_clsgztt1', 'test_clsgztt2', 'test_clsgztt3', 'test_clsgztt4', 'test_clsgztt5', 'test_clsgztt6', 'test_clsgztt7', 'test_clsgztt8', 'test_clsgztt9', 'test_clsgztt10','uc', 'mi', 'oppo', 'vivo', 'mz','yyb', 'juedi', 'yqwb','h5_9130','h5_kuku','wende_ad','yx7477','h5_qq_ad','h5_qq_ios','h5_changwan','ios_37','h5_wende','h5_changxiang','ios_sh','dev'],#pf范围等于X
	},	#三个条件均满足时，此玩家将从排行榜中排除


#################################城防军设置#################################
	'name':'510034',		#自卫军进攻
	'info':'510041',		#斥候情报中的信息
	'guard_add':60,		#在城防军数量不满足上限数量时，每60分钟恢复1个（这个频率完全有配置决定，但每次回复的数量可以被城池建设增加）
	'guard_hero':'herorange_base',		#城防军英雄池
	'guard_base':'country',		#城防军机器人池
	'guard_enemy_power':1.3,	#城防军推荐战力系数，敌人实际战力*enemy_power=推荐战力
	'guard_lv':1,		#城防军等级系数
	'volunteers':5,		#各国义勇军攻击周期，服务器每x天攻击一次非本国的腹地城池，义勇军数据根据目标城池决定，等级，数量，固定精英
	'volunteers_time':[12,0],		#义勇军攻击时间点
	'elites_odds':0.2,		#每一波城防军有X%的几率成为精锐城防军
	'elites_lv':3,		#精锐城防军最终等级+3
	'gen_odds':0.05,		#每一波城防军有X%的几率成为城防军大将
	'gen_lv':5,		#城防军大将最终等级+5
	'model':'',		#在世界地图中的模型
	'form':2,		#部队形式，1单部队，2四部队
	'gen_troop':[           #城防军大将会随机携带的额外属性
           {'formation':[1,{}]},
           {'formation':[2,{}]},
           {'formation':[3,{}]},
           {'formation':[4,{}]},
           {'formation':[5,{}]},
           {'formation':[6,{}]},
        ],	
	'elites_troop':[           #城防军精锐会随机携带的额外属性
           {'formation':[1,{}]},
           {'formation':[2,{}]},
           {'formation':[3,{}]},
           {'formation':[4,{}]},
           {'formation':[5,{}]},
           {'formation':[6,{}]},
        ],

	#城防军的等级：基础等级+城池额外等级+士兵额外等级=一只城防军的等级
		#基础等级：
			#城池初始等级 与 世界等级，取用较高者
			#城池初始等级：‘city’-‘troop’数组中最后一位
		#城池额外等级：当城池归属国家与‘city’-‘faith’数组中首位相同时，生效，数据来源：‘city’-‘faith’数组中最后一位
		#士兵额外等级：只有当这只城防军部队成为精英或大将时才会生效，'elites_lv'和'gen_lv'
	#各国腹地配置依照城池buff中势力归属配置（‘city’—‘faith’数组中首位）
	#NPC攻城时，战斗结束/熄火后，只记剩余数量，然后会转化成相应阵营的城防军

#################################季节面板#################################
	'season':{
		'info':'510050',		#通用提示
		'limit':'510051',		#【{0}在官邸到达{1}开启】
		'0':{		#春
			'name':'510052',		#名称
			'scenery':'season0.jpg',		#插画
			'entry':[
				{'name':'510053','info':'510054','limit':['add_pk_yard',7]}		#条目1，报名参战，限制，X级开启，为0时不显示
			]
		},
		'1':{		#夏
			'name':'510067',		#名称
			'scenery':'season1.jpg',		#插画
			'entry':[
				{'name':'510053','info':'510054','limit':['add_pk_yard',7]},		#条目1，报名参战，限制，X级开启，为0时不显示
				{'name':'510055','info':'510056','limit':['add_climb',7]}		#条目2，过关斩将，限制，X级开启，为0时不显示
			]
		},
		'2':{		#秋
			'name':'510068',		#名称
			'scenery':'season2.jpg',		#插画
			'entry':[
				{'name':'510061','info':'510062','limit':['add_pk_yard',7]},		#条目1，比武大会，限制，X级开启，为0时不显示
				#{'name':'510059','info':'510060','limit':[]},		#条目2，秋收，限制，X级开启，为0时不显示
			]
		},
		'3':{		#冬
			'name':'510069',		#名称
			'scenery':'season3.jpg',		#插画
			'entry':[
				{'name':'510053','info':'510054','limit':['add_pk_yard',7]},		#条目1，报名参战，限制，X级开启，为0时不显示
				{'name':'510065','info':'510066','limit':[]},		#条目2，年度战功，限制，X级开启，为0时不显示
				{'name':'510063','info':'510064','limit':[]},		#条目3，年度奖励，限制，X级开启，为0时不显示
			]
		}
	},
#################################封地内领取物品的气泡#################################
	'science_show':'item059.png',		#科技功能在领取物品时显示的宝箱
	'pve_show':'item059.png',		#过关斩将在封地内的建筑上领取奖励时显示的内容




####################################################################
###########副将配置###################################################
#####################################################################

  'adjutant_level':18,  #可以装备副将的星级等级限制

  'adjutant_skill_hero':[['adjutantH00','adjutantH10'],['adjutantH01','adjutantH11'],['adjutantH02','adjutantH12']],  #副将主动技能，对应英雄的type的012值在前军和后军能提供的副将主动技能

  'adjutant_skill_army':['adjutantA0','adjutantA1','adjutantA2','adjutantA3'],  #副将兵种技能，对应兵种的type0123，能提供的副将兵种技能


  'adjutant_wash':[50,70,100,140,200],  #副将之宝物洗练评分

  'adjutant_enhance':[0,25,50,75,100,125,175,225,275,325,375,450,600,700,800,900,1300,1500,1700,1900,2500,2700,2900,3100,3800],  #副将之强化评分













####################################################################
###########阵法配置###################################################
#####################################################################


  'arr_level':[



   
          [0,2],

          [0,3],

          [0,4],

          [0,5],

          [0,6],

          [0,7],

          [0,8],

          [0,9],

          [0,10],

          [0,12],

          [0,14],

          [0,16],

          [0,18],

          [0,20],

          [0,22],

          [0,24],

          [0,26],

          [0,28],

          [0,30],

          [1,34],

          [1,38],

          [1,42],

          [1,46],

          [1,50],

          [1,56],

          [1,62],

          [1,68],

          [1,74],

          [1,80],

          [2,89],

          [2,98],

          [2,107],

          [2,116],

          [2,125],

          [2,137],

          [2,149],

          [2,161],

          [2,173],

          [2,185],

          [3,201],

          [3,217],

          [3,233],

          [3,249],

          [3,265],

          [3,286],

          [3,307],

          [3,328],

          [3,349],

          [3,370],

          [4,397],

          [4,424],

          [4,451],

          [4,478],

          [4,505],

          [4,539],

          [4,573],

          [4,607],

          [4,641],

          [4,675],

          [5,717],






    ],
 
     'arr_quality':[   #进阶品质需要的条件

       [0,0,5],    #【升绿色无条件】，需要的心得数量

       [2,1,20],    #【升蓝色需要有2个相同类型阵法达到绿品质】，需要的心得数量
       [3,2,80],    #【升紫色需要有3个相同类型阵法达到蓝品质】，需要的心得数量
       [4,3,300],    #【升橙色需要有4个相同类型阵法达到紫品质】，需要的心得数量
       [5,4,600],    #【升红色需要有5个相同类型阵法达到橙品质】，需要的心得数量



                     ],

     'arr_forget_item':'item1064', #阵法遗忘的道具，消耗该道具返还所有阵法的升级材料和进阶材料

     'arr_cost':{   #每个阵法ID对应的进阶材料和升级材料
 
       1:['item170','item164'],    
       2:['item171','item165'],    
       3:['item172','item166'],     
       4:['item173','item167'],  
       5:['item174','item168'],  
       6:['item175','item169'], 

                     },

########################################################
#########觉醒碎片补发######################
########################################################
	'msg_awaken_repair':['550030','550031'],		#补发英雄碎片的邮件标题和内容


########################################################
#########国战任务######################
########################################################
	'fight_task':{		#国战任务（首次在开服第4天，第五天没别的功能，所以在第五天会连续开启一次（与擂台赛，襄阳战，黄巾二军冲突，优先级最低，其他活动未开启时，开启这个功能））
		'expand_day':[3,3],		#合服前|后，开服前X天不激活，如果为0，表示一直不激活，正式配置[3,3]
		'gap':[1,1],		#开启间隔,每开启两次，间隔1次，现在这个逻辑用不到，不要改
		'time_notice':3600,		#提前1300秒显示预告入口（不可点击）
		'show_talk1':['hero751','guide_npc002','500048'],		#推送时使用的立绘，名称，文本，发布任务的文本
		'show_talk2':['hero751','guide_npc002','500052'],		#推送时使用的立绘，名称，文本，未开启的文本
		'show_talk3':['hero751','guide_npc002','500053'],		#推送时使用的立绘，名称，文本，至少完成一个任务
		'show_talk4':['hero751','guide_npc002','500055'],		#推送时使用的立绘，名称，文本，一个任务都没完成
		'time_begin':[20,0],		#发放任务时间
		'time_end':[21,0],		#任务结算时间
		'merit_add':[[20,0] ,[22,0],1.5],		#战功翻倍时间段和倍率
		'task_buff':[0.25,0.25,3600],		#[0]伤害和免伤 [1]行军速度加成，[2]持续时间（秒）
		'exclude_lv':20,		#排除刻度，这个值和世界等级取较大值【刻度数组中，攻城等级高于这个值的城池，被排除掉】
		'city_gauge':[		#城池标尺，需保证长度相等，且尽量为奇数
			[
				[0,1],		#魏国进攻蜀国的标尺
				[
                                   ['129','161'],['127','114','117'],                         #魏国腹地
                                   ['123','113'],['98','96','111','241'],['92','102','104'],        #魏国关外
                                   ['90','88'],['87','107','106'],['14','80'],                       #中立争夺
                                   ['16','272','78'],['18','83','82','13'],['20','76'],            #蜀国关外
                                   ['22','23','70'],['25','26']                              #蜀国腹地
                                ]
			],
			[
				[1,2],		#蜀国进攻吴国的标尺
				[
                                   ['59','74'],['65','44','51'],
                                   ['55','283'],['323','319','285','280'],['317','311','288'],
                                   ['309','296'],['307','293','252'],['306','254'],
                                   ['303','301','255'],['326','246','249','329'],['331','344'],
                                   ['338','347','342'],['340','358']
                                ]
			],
			[
				[2,0],		#吴国进攻魏国的标尺
				[
                                   ['370','364'],['388','224','221'],
                                   ['203','218'],['200','215','209','197'],['194','191','225'],
                                   ['190','227'],['184','210','154'],['172','170'],
                                   ['174','169','186'],['177','180','230','188'],['140','151'],
                                   ['138','157','232'],['142','159']
                                ]
			],
		],
	#上党，荥阳，，代郡，弘农，安邑，
	#河曲，通关，，上郡，朔方，泾阳，南阳，，盐川，北地，高奴
	#灵州，安定，，石城，咸阳，新平，，狄道，秦川，
	#陇西，五丈原，陈仓，，洮阳，武都，天水，金城，，沓中，散关，
	#江油，剑阁，阆中，，绵竹，都安，


	#且兰，朱提，，江州，泸水，沙口，
	#建宁，宕渠，，兴古，昭平，临江，涪陵，，牂牁，郁林，巫山
	#潭中，五溪，，河池，建平，武陵，，，桂林，夷陵，
	#零陵，武冈，公安，，桂阳，江陵，汉寿，衡阳，，耒阳，巴丘，
	#茶陵，赤壁，长沙，，庐陵，宁都，


	#平阳，鄱阳，，富春，余杭，芜湖，
	#吴郡，庐江，，广陵，无为，临淮，淮阴，，下邳，琅琊，颍上
	#东武，固始，，东莱，寿春，小沛，，，北海，曲阜，
	#乐安，东阿，蓬莱，，南皮，渤海，汝南，东牟，，平原，定陶，
	#信都，谯郡，许昌，，巨鹿，陈留，
		'score':{		#分数计算
			'task2':[		#每个国家，累计记录最近X次国家任务(数组长度+1就是记录的次数)，每次国家任务，胜利加X分，失败减X分
				[0,1.15],	#最近一次
				[1,1],	        #倒数第二次
				[2,0.9],	#倒数第三次
				[3,0.8],	#倒数第四次
				[4,0.6],
				[5,0.3],	
			],
			'power':[0.2,1,5],		#国力排行对比，每高X%加1分，每低X%减1分【(a-b)/min(a,b)/系数*分数，向下取整】,最大影响正负4分
		}
	},

########################################################
#########合服放心充######################天下大势最后一页
########################################################
	'bold_recharge':{
		'blind_time':36,		#不显示预告的天数，开服的前18天
		'inform_time':40,		#预告总天数，用这个值减去当前开服天数就是倒计时天数
		'text1':'530109',		#倒计时的文本，{0}带入剩余天数
		'text2':'530110',		#已经进入合服队列的文本
		'text3':'530111',		#下方文字
	},


        #计算综合国力系数           [都城数,城池总数,活跃高战数,活跃中坚数,排名积分,活跃尖端战力,活跃总战力,活跃实际充值,活跃虚拟充值,活跃Coin]
        'country_power_multiple' :  [100,   10,      0,         0,         0.005,   0.001,       0.0001,    0.03,        0.01,        0.0005],  

        #国家活跃参数
       "country_active":
                       [
                           [[10,10000000,0.5,1.5],[20,15000000,0.5,1.5],[30,20000000,0.5,1.5]],   #未合服参数 对应[开服第几天，分母大小，下限，上限]
                           [[10,40000000,0.5,1.75]], #1次合服参数 对应[开服第几天，分母大小，下限，上限]
                           [[10,100000000,0.5,2]], #2次合服参数 对应[开服第几天，分母大小，下限，上限]
                           [[10,150000000,0.5,2]], #3次合服参数 对应[开服第几天，分母大小，下限，上限]
                           [[10,180000000,0.5,2]], #4次合服参数 对应[开服第几天，分母大小，下限，上限]
                           [[10,200000000,0.5,2]], #5次合服参数 对应[开服第几天，分母大小，下限，上限]
                           [[10,225000000,0.5,2]], #6次合服参数 对应[开服第几天，分母大小，下限，上限]
                           [[10,250000000,0.5,2]], #7次合服参数 对应[开服第几天，分母大小，下限，上限]
                      ],

	'pve_lv':{#高级pveNPC战力对应  世界高玩等级
	   'range':30,		#服务器单将战力前x名的平均值
	   'convert':[		#战力转换为等级
              [69150000,400],
              [68595000,399],
              [68040000,398],
              [67485000,397],
              [66930000,396],
              [66375000,395],
              [65820000,394],
              [65265000,393],
              [64710000,392],
              [64155000,391],
              [63600000,390],
              [63075000,389],
              [62550000,388],
              [62025000,387],
              [61500000,386],
              [60975000,385],
              [60450000,384],
              [59925000,383],
              [59400000,382],
              [58875000,381],
              [58350000,380],
              [57840000,379],
              [57330000,378],
              [56820000,377],
              [56310000,376],
              [55800000,375],
              [55290000,374],
              [54780000,373],
              [54270000,372],
              [53760000,371],
              [53250000,370],
              [52755000,369],
              [52260000,368],
              [51765000,367],
              [51270000,366],
              [50775000,365],
              [50280000,364],
              [49785000,363],
              [49290000,362],
              [48795000,361],
              [48300000,360],
              [47835000,359],
              [47370000,358],
              [46905000,357],
              [46440000,356],
              [45975000,355],
              [45510000,354],
              [45045000,353],
              [44580000,352],
              [44115000,351],
              [43650000,350],
              [43215000,349],
              [42780000,348],
              [42345000,347],
              [41910000,346],
              [41475000,345],
              [41040000,344],
              [40605000,343],
              [40170000,342],
              [39735000,341],
              [39300000,340],
              [38880000,339],
              [38460000,338],
              [38040000,337],
              [37620000,336],
              [37200000,335],
              [36780000,334],
              [36360000,333],
              [35940000,332],
              [35520000,331],
              [35100000,330],
              [34710000,329],
              [34320000,328],
              [33930000,327],
              [33540000,326],
              [33150000,325],
              [32760000,324],
              [32370000,323],
              [31980000,322],
              [31590000,321],
              [31200000,320],
              [30840000,319],
              [30480000,318],
              [30120000,317],
              [29760000,316],
              [29400000,315],
              [29040000,314],
              [28680000,313],
              [28320000,312],
              [27960000,311],
              [27600000,310],
              [27285000,309],
              [26970000,308],
              [26655000,307],
              [26340000,306],
              [26025000,305],
              [25710000,304],
              [25395000,303],
              [25080000,302],
              [24765000,301],
              [24450000,300],
              [24165000,299],
              [23880000,298],
              [23595000,297],
              [23310000,296],
              [23025000,295],
              [22740000,294],
              [22455000,293],
              [22170000,292],
              [21885000,291],
              [21600000,290],
              [21345000,289],
              [21090000,288],
              [20835000,287],
              [20580000,286],
              [20325000,285],
              [20070000,284],
              [19815000,283],
              [19560000,282],
              [19305000,281],
              [19050000,280],
              [18840000,279],
              [18630000,278],
              [18420000,277],
              [18210000,276],
              [18000000,275],
              [17790000,274],
              [17580000,273],
              [17370000,272],
              [17160000,271],
              [16950000,270],
              [16762500,269],
              [16575000,268],
              [16387500,267],
              [16200000,266],
              [16012500,265],
              [15825000,264],
              [15637500,263],
              [15450000,262],
              [15262500,261],
              [15075000,260],
              [14902500,259],
              [14730000,258],
              [14557500,257],
              [14385000,256],
              [14212500,255],
              [14040000,254],
              [13867500,253],
              [13695000,252],
              [13522500,251],
              [13350000,250],
              [13189500,249],
              [13029000,248],
              [12868500,247],
              [12708000,246],
              [12547500,245],
              [12387000,244],
              [12226500,243],
              [12066000,242],
              [11905500,241],
              [11745000,240],
              [11598000,239],
              [11451000,238],
              [11304000,237],
              [11157000,236],
              [11010000,235],
              [10863000,234],
              [10716000,233],
              [10569000,232],
              [10422000,231],
              [10275000,230],
              [10141500,229],
              [10008000,228],
              [9874500,227],
              [9741000,226],
              [9607500,225],
              [9474000,224],
              [9340500,223],
              [9207000,222],
              [9073500,221],
              [8940000,220],
              [8814000,219],
              [8688000,218],
              [8562000,217],
              [8436000,216],
              [8310000,215],
              [8184000,214],
              [8058000,213],
              [7932000,212],
              [7806000,211],
              [7680000,210],
              [7557000,209],
              [7434000,208],
              [7311000,207],
              [7188000,206],
              [7065000,205],
              [6942000,204],
              [6819000,203],
              [6696000,202],
              [6573000,201],
              [6450000,200],
              [6331500,199],
              [6213000,198],
              [6094500,197],
              [5976000,196],
              [5857500,195],
              [5739000,194],
              [5620500,193],
              [5502000,192],
              [5383500,191],
              [5265000,190],
              [5161500,189],
              [5058000,188],
              [4954500,187],
              [4851000,186],
              [4747500,185],
              [4644000,184],
              [4540500,183],
              [4437000,182],
              [4333500,181],
              [4230000,180],
              [4147500,179],
              [4065000,178],
              [3982500,177],
              [3900000,176],
              [3817500,175],
              [3735000,174],
              [3652500,173],
              [3570000,172],
              [3487500,171],
              [3405000,170],
              [3337500,169],
              [3270000,168],
              [3202500,167],
              [3135000,166],
              [3067500,165],
              [3000000,164],
              [2932500,163],
              [2865000,162],
              [2797500,161],
              [2730000,160],
              [2674500,159],
              [2619000,158],
              [2563500,157],
              [2508000,156],
              [2452500,155],
              [2397000,154],
              [2341500,153],
              [2286000,152],
              [2230500,151],
              [2175000,150],
              [2130000,149],
              [2085000,148],
              [2040000,147],
              [1995000,146],
              [1950000,145],
              [1905000,144],
              [1860000,143],
              [1815000,142],
              [1770000,141],
              [1725000,140],
              [1687500,139],
              [1650000,138],
              [1612500,137],
              [1575000,136],
              [1537500,135],
              [1500000,134],
              [1462500,133],
              [1425000,132],
              [1387500,131],
              [1350000,130],
              [1320000,129],
              [1290000,128],
              [1260000,127],
              [1230000,126],
              [1200000,125],
              [1170000,124],
              [1140000,123],
              [1110000,122],
              [1080000,121],
              [1050000,120],
              [1026000,119],
              [1002000,118],
              [978000,117],
              [954000,116],
              [930000,115],
              [906000,114],
              [882000,113],
              [858000,112],
              [834000,111],
              [810000,110],
              [791250,109],
              [772500,108],
              [753750,107],
              [735000,106],
              [716250,105],
              [697500,104],
              [678750,103],
              [660000,102],
              [641250,101],
              [622500,100],
              [609750,99],
              [597000,98],
              [584250,97],
              [571500,96],
              [558750,95],
              [546000,94],
              [533250,93],
              [520500,92],
              [507750,91],
              [495000,90],
              [485250,89],
              [475500,88],
              [465750,87],
              [456000,86],
              [446250,85],
              [436500,84],
              [426750,83],
              [417000,82],
              [407250,81],
              [397500,80],
              [390000,79],
              [382500,78],
              [375000,77],
              [367500,76],
              [360000,75],
              [352500,74],
              [345000,73],
              [337500,72],
              [330000,71],
              [322500,70],
              [317250,69],
              [312000,68],
              [306750,67],
              [301500,66],
              [296250,65],
              [291000,64],
              [285750,63],
              [280500,62],
              [275250,61],
              [270000,60],
              [265500,59],
              [261000,58],
              [256500,57],
              [252000,56],
              [247500,55],
              [243000,54],
              [238500,53],
              [234000,52],
              [229500,51],
              [225000,50],


	   ],
	},





####################抵抵抵####################
####################扣扣扣####################
####################券券券####################
	'sale_server_time_out':10,		#抵扣券过期后，服务器仍认可效用的分钟数
	'sale_tips':{		#折扣券临期提示
		'open':1,		#该功能是否显示的开关，0=不显示，1=显示
		'time':1440,		#分钟，当有抵扣券的剩余时间小于等于这个值时，则在登陆游戏时弹出提示框
		'show_hero':'hero701',		#面板上的英雄立绘
		'title':'sale_pay_18',		#标题文本
	},
	'sale_pay':{		#折扣券
		'pay201':['pay3','sale201',0],		#使用'pay201'充值时：获得'pay3'的充值内容，扣除'sale201'的券，显示图标
		'pay202':['pay4','sale202',1],
		'pay203':['pay4','sale203',0],
		'pay204':['pay5','sale204',1],
		'pay205':['pay5','sale205',0],
		'pay206':['pay6','sale206',1],
		'pay207':['pay6','sale207',0],
		'pay208':['pay7','sale208',2],
		'pay209':['pay7','sale209',1],
		'pay210':['pay7','sale210',0],
		'pay211':['pay8','sale211',2],
		'pay212':['pay8','sale212',1],
		'pay213':['pay8','sale213',0],
		#'pay214':['pay9','sale214',1],
		#'pay215':['pay9','sale215',0],
		#'pay216':['pay10','sale216',0],
		#'pay217':['pay11','sale217',0],
		#'pay218':['pay12','sale218',0],
	},
	'sale_depot':{		#折扣券库
		#'saledepot000':[[中间值，持续时间，[屏蔽渠道]，['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#投放时，直接填#'saledepot00X'
		'saledepot001':[ 'sale201',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档68—60，持续3天
		'saledepot002':[ 'sale201',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档68—60，持续5天
		'saledepot003':[ 'sale201',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档68—60，持续7天
		'saledepot004':[ 'sale201',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档68—60，持续15天
		'saledepot005':[ 'sale201',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档68—60，持续30天
		'saledepot006':[ 'sale202',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—98，持续3天
		'saledepot007':[ 'sale202',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—98，持续5天
		'saledepot008':[ 'sale202',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—98，持续7天
		'saledepot009':[ 'sale202',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—98，持续15天
		'saledepot010':[ 'sale202',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—98，持续30天
		'saledepot011':[ 'sale203',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—113，持续3天
		'saledepot012':[ 'sale203',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—113，持续5天
		'saledepot013':[ 'sale203',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—113，持续7天
		'saledepot014':[ 'sale203',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—113，持续15天
		'saledepot015':[ 'sale203',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档128—113，持续30天
		'saledepot016':[ 'sale204',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—278，持续3天
		'saledepot017':[ 'sale204',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—278，持续5天
		'saledepot018':[ 'sale204',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—278，持续7天
		'saledepot019':[ 'sale204',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—278，持续15天
		'saledepot020':[ 'sale204',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—278，持续30天
		'saledepot021':[ 'sale205',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—298，持续3天
		'saledepot022':[ 'sale205',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—298，持续5天
		'saledepot023':[ 'sale205',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—298，持续7天
		'saledepot024':[ 'sale205',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—298，持续15天
		'saledepot025':[ 'sale205',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档328—298，持续30天
		'saledepot026':[ 'sale206',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—548，持续3天
		'saledepot027':[ 'sale206',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—548，持续5天
		'saledepot028':[ 'sale206',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—548，持续7天
		'saledepot029':[ 'sale206',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—548，持续15天
		'saledepot030':[ 'sale206',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—548，持续30天
		'saledepot031':[ 'sale207',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—588，持续3天
		'saledepot032':[ 'sale207',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—588，持续5天
		'saledepot033':[ 'sale207',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—588，持续7天
		'saledepot034':[ 'sale207',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—588，持续15天
		'saledepot035':[ 'sale207',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档648—588，持续30天
		'saledepot036':[ 'sale208',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—818，持续3天
		'saledepot037':[ 'sale208',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—818，持续5天
		'saledepot038':[ 'sale208',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—818，持续7天
		'saledepot039':[ 'sale208',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—818，持续15天
		'saledepot040':[ 'sale208',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—818，持续30天
		'saledepot041':[ 'sale209',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—848，持续3天
		'saledepot042':[ 'sale209',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—848，持续5天
		'saledepot043':[ 'sale209',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—848，持续7天
		'saledepot044':[ 'sale209',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—848，持续15天
		'saledepot045':[ 'sale209',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—848，持续30天
		'saledepot046':[ 'sale210',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—898，持续3天
		'saledepot047':[ 'sale210',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—898，持续5天
		'saledepot048':[ 'sale210',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—898，持续7天
		'saledepot049':[ 'sale210',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—898，持续15天
		'saledepot050':[ 'sale210',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档998—898，持续30天
		'saledepot051':[ 'sale211',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1498，持续3天
		'saledepot052':[ 'sale211',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1498，持续5天
		'saledepot053':[ 'sale211',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1498，持续7天
		'saledepot054':[ 'sale211',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1498，持续15天
		'saledepot055':[ 'sale211',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1498，持续30天
		'saledepot056':[ 'sale212',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1598，持续3天
		'saledepot057':[ 'sale212',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1598，持续5天
		'saledepot058':[ 'sale212',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1598，持续7天
		'saledepot059':[ 'sale212',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1598，持续15天
		'saledepot060':[ 'sale212',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1598，持续30天
		'saledepot061':[ 'sale213',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1648，持续3天
		'saledepot062':[ 'sale213',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1648，持续5天
		'saledepot063':[ 'sale213',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1648，持续7天
		'saledepot064':[ 'sale213',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1648，持续15天
		'saledepot065':[ 'sale213',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档1998—1648，持续30天
		#'saledepot066':[ 'sale214',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2298，持续3天
		#'saledepot067':[ 'sale214',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2298，持续5天
		#'saledepot068':[ 'sale214',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2298，持续7天
		#'saledepot069':[ 'sale214',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2298，持续15天
		#'saledepot070':[ 'sale214',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2298，持续30天
		#'saledepot071':[ 'sale215',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2598，持续3天
		#'saledepot072':[ 'sale215',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2598，持续5天
		#'saledepot073':[ 'sale215',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2598，持续7天
		#'saledepot074':[ 'sale215',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2598，持续15天
		#'saledepot075':[ 'sale215',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档2998—2598，持续30天
		#'saledepot076':[ 'sale216',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档3998—3298，持续3天
		#'saledepot077':[ 'sale216',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档3998—3298，持续5天
		#'saledepot078':[ 'sale216',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档3998—3298，持续7天
		#'saledepot079':[ 'sale216',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档3998—3298，持续15天
		#'saledepot080':[ 'sale216',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档3998—3298，持续30天
		#'saledepot081':[ 'sale217',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档4998—4498，持续3天
		#'saledepot082':[ 'sale217',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档4998—4498，持续5天
		#'saledepot083':[ 'sale217',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档4998—4498，持续7天
		#'saledepot084':[ 'sale217',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档4998—4498，持续15天
		#'saledepot085':[ 'sale217',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档4998—4498，持续30天
		#'saledepot086':[ 'sale218',4320,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档6498—5898，持续3天
		#'saledepot087':[ 'sale218',7200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档6498—5898，持续5天
		#'saledepot088':[ 'sale218',10080,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档6498—5898，持续7天
		#'saledepot089':[ 'sale218',21600,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档6498—5898，持续15天
		#'saledepot090':[ 'sale218',43200,['com.djcy.game.sg.p3','txxx.game.p3','meng.zqwz.pay3']],#档6498—5898，持续30天
	},

	#投放格式：数量为整数，时间为分钟,屏蔽渠道
		#{'sale201':[num,time,[hw]]}
		#['sale205',[2,300,['hw','ios_djcy']]]
	#时间对照：3天：4320，5天：7200，7天：10080
	#对照表：
	#sale201：68—60
	#sale202：128—98
	#sale203：128—113
	#sale204：328—278
	#sale205：328—298
	#sale206：648—548
	#sale207：648—588
	#sale208：998—818
	#sale209：998—848
	#sale210：998—898
	#sale211：1998—1498
	#sale212：1998—1598
	#sale213：1998—1648
	#sale214：2998—2298
	#sale215：2998—2598
	#sale216：3998—3298
	#sale217：4998—4498
	#sale218：6498—5898

	'coin_recyle_type':{
		'kill_science_cd':'秒科技CD',
		'accept_sys_gift_msg':'邮件发奖',
	},
###############表情##############
"cfg_face":{#1普通免费,2永久卡,3以后再说
    '1':{		
    		"name":'face_text0',
    		"size":[25,25],
    		"ids":["com_dk","com_dy","com_fn","com_hj","com_hx","com_kl","com_md","com_pt","com_s","com_wx","com_bishi","com_damuzhi","com_outu","com_tuxue","com_wen","com_wuliao","com_xianhua","com_xuedao","com_yihuo","com_zaijian"],            
    },  
    '2':{
    		"name":'face_text1',
    		"size":[40,40],
    		"ids":["vip_bj","vip_ds","vip_lh","vip_qq","vip_qy","vip_tt","vip_wd","vip_zj","vip_zyg","vip_zz"],
    },      
    '3':{
    		"name":'face_text2',
    		"size":[50,50],
    		"ids":["spec_cattle01","spec_cattle02","spec_cattle03","spec_cattle04","spec_cattle05",],
    },  
},



#####宿敌配置##########################

  "counter_merge":1,#一次合服后开启


  "counter_exp":[50,100,150,200,300,200,250,300,350,500],# 宿敌等级需要的英雄碎片

  "counter_seat":[["merge",1],["science","121"],["science","126"],["office","1401"],["office","1401"]],#数组每个位置对应一个仇敌位置，science_X表示开启宿敌位置对应的科技,merge_X表示开启对应的合服次数

  "counter_level":[['merge',1,5],['science','131']],#当前的宿敌等级上限，将当前数组符合条件的全部相加等于当前宿敌等级上限，merge_x表示合服次数的等级，science_X表示玩家对应科技的等级


  "counter_free":7,#免费化解一次的时间间隔。单位天
  "counter_coin":500,#收费化解的价格，单位coin

  "counter_color":[1,2,4,6,8,10],# 宿敌等级对应的颜色

#####奇士配置##########################

  "sp_army_merge":1,  #一次合服后开启
  "sp_army_day":14,  #多少天后开启
  "sp_army_number":[500,1000,2000,4000,10000],   #5个级别升需要的碎片数量，存储等级从0开始，最高到4
  "sp_army_lv":1,   #允许当前最高存储等级，（填1时最高升到II）
  "sp_army_begin_data":[2020,7,3],#版本上线日期
  "sp_army_list":['sp3250','sp3251','sp3350','sp3351',"sp3050","sp3051","sp3150","sp3151"],#活动列表
#####魂玉配置##########################

  "soul_merge":3,#3次合服后开启
  'soul_upgrade':[
#消耗的当前星级魂石数量，幸运暴击几率，消耗的材料itemID，消耗的item数量
#消耗的魂石必须ID相同
#使用的配置数组0对应魂石的item_type
[[3,[[1,70],[2,30]],'',0],[3,[[1,70],[2,30]],'',0],],
[[3,[[1,80],[2,20]],'',0],[3,[[1,80],[2,20]],'',0],],
[[4,[[1,85],[2,15]],'',0],[4,[[1,85],[2,15]],'',0],],
[[4,[[1,90],[2,10]],'',0],[4,[[1,90],[2,10]],'',0],],
[[5,[[1,94],[2,6]],'',0],[5,[[1,94],[2,6]],'',0],],
[[5,[[1,95],[2,5]],'',0],[5,[[1,95],[2,5]],'',0],],
[[6,[[1,96],[2,4]],'',0],[6,[[1,96],[2,4]],'',0],],
[[6,[[1,97],[2,3]],'',0],[6,[[1,97],[2,3]],'',0],],
[[6,[[1,100],[2,0]],'',0],[6,[[1,100],[2,0]],'',0],],



],







##############QQ游戏大厅################
        'qq_blue':{
                'appId':'1109956380',
                'appKey':'oNO5I36fugQGkcRM',
                'blue_right_notice':'',
                'blue_link':'//gamevip.qq.com/?ADTAG=VIP.WEB.ZQWZ',
                'blue_new_reward': {'item003':3,'item030':3,'item032':3,'item030':3,},	#'新手奖励'
                'qqdt_new_reward': {'item003':3,'item030':3,'item032':3,'item030':3,},	#'qq大厅新手奖励'
                'blue_lvup_reward': {	#蓝钻升级礼包     
'5':{'item701':50,'item001':5},
'10':{'item701':100,'item002':5},
'15':{'item701':150,'item003':5},
'20':{'item701':200,'item003':10},
'25':{'item701':300,'item004':10},


                },
'qqdt_lvup_reward': {	#qq大厅升级礼包     
'5':{'item701':50,'item001':5},
'10':{'item701':100,'item002':5},
'15':{'item701':150,'item003':5},
'20':{'item701':200,'item003':10},
'25':{'item701':300,'item004':10},


                },
                     
'qqdt_day_reward': {'gold':5000,'item020':5},


  
                'blue_day_reward': {	#每日奖励
'1':{'gold':5000,'item020':5},
'2':{'gold':10000,'item020':7},
'3':{'gold':15000,'item020':10},
'4':{'gold':20000,'item021':2},
'5':{'gold':25000,'item021':3},
'6':{'gold':30000,'item021':4},
'7':{'gold':35000,'item021':5},


                
                },
                'blue_big_box':{'gold':5000,'item030':3,'item037':5,},#蓝钻豪华版礼包
                'blue_year_box':{'gold':10000,'item030':5,'item037':10,},	#蓝钻年费礼包
        },




############################防止未成年人沉迷网络游戏################################

	'real_name':{		#实名认证
		'time':60,		#体验时间，单位：分钟
		'interval':15,		#体验间隔，单位：天 （记录设备id，15天之后才能创建新的账号）
		#'real_tips':根据国家新闻出版署发布的《关于防止未成年人沉迷网络游戏的通知》您需要进行实名制认证方可继续游戏，点击“确认”前往认证
		'pfs': [],		#不需要单独认证的pf
	},
	'indulge':{		#防沉迷
		'pfs': [],		#屏蔽pf
		'curfew':[[8,1],[21,59]],		#游戏可用时间，早8点到晚22点
		#'curfew_tips':根据国家新闻出版署发布的《关于防止未成年人沉迷网络游戏的通知》每日22时至次日8时为防沉迷时间段，不可登陆游戏
		'day_time':90,		#每天可登录游戏90分钟
		'holiday_time':180,		#法定节假日每天可登录180分钟
		'holiday':[				#法定节假日为以下日期
			[1,1],[5,1],[10,1],[10,2],[10,3]
		],
		#'time_tips':根据国家新闻出版署发布的《关于防止未成年人沉迷网络游戏的通知》您今日的游戏时间已用尽
		'pay_limit':[		#充值限额
			[[0,7],0,0],	#年龄段，单笔限额，每月限额
			[[8,15],50,200],	#年龄判断，单笔最大充值限额，每个月总计充值限额
			[[16,17],100,400],
		],
		#'pay_tips1':根据国家新闻出版署发布的《关于防止未成年人沉迷网络游戏的通知》您的账号不可充值
		#'pay_tips2':根据国家新闻出版署发布的《关于防止未成年人沉迷网络游戏的通知》您的账号单次充值不能超过{0}元，本月已经累计充值{1}/{2}元
	},



############################新版战报相关内容################################
	'war_report':{		#战报
		'save_log':0,		#是否存储战报到mongodb 0：不存 1：存
		'clear_time':[[4,59],24*60*60],		#每日清理战报时间点
		'info':'war_report_info18',		#底部文字
		'share_cd':5,		#分享战报CD
		'collect_num':20,		#每个玩家最多收藏20条战报
		'report_name':[		#根据战斗场数，显示不同的标题
			[1000,99999,'war_report_info51'],
			[500,999,'war_report_info52'],
			[100,499,'war_report_info53'],
			[50,99,'war_report_info54'],
		],
	},





###########################天气（气象）相关内容###########################
	'weather':{
		'switch':1,		#功能总开关，0关闭，1开启
		'merge_times':4,		#合服次数，大于等于即为开启

		#无天气0，风沙1，暴雨2，浓雾3，大雪4

		'weather_info':'weather_info05',	#整体说明文字
		'options':{		#天气显示相关内容
			'1':{	#风沙
				'alpha':1,			#大地图中的显示透明度
				'info':"weather_info06",	#文字说明
				'color':"#ffe588",		#文字颜色
				'scale':[0.7,1,1.5,1.8,2,2]	#大地图中，根据cityType的缩放比例，数组位数对应city-type:0~5
			},
			'2':{	#暴雨
				'alpha':1,
				'info':"weather_info07",
				'color':"#47d9b2",
				'scale':[0.7,1,1.5,1.8,2,2]
			},
			'3':{	#浓雾
				'alpha':0.85,
				'info':"weather_info08",
				'color':"#dec2a7",
				'scale':[0.6,0.8,1.3,1.6,1.6,1.6]
			},
			'4':{	#大雪
				'alpha':1,
				'info':"weather_info09",
				'color':"#88dcff",
				'scale':[0.7,1,1.5,1.8,2,2]
			}
		},

		'pathogen':{		#可以生成天气原体的cityType，和该cityType的扩散度【度，几率】
			5:[1,0.8,0.5,0.3,0.1],
			4:[1,0.8,0.5,0.3],
			3:[0.5,0.3,0.1],
			2:[0.3,0.1],	
		},
		#同一个天气原体，在计算扩散城池时，遍历到的每个城池只能没计算一次是否感染
		'generate':{				#每种季节，获得天气原体的生成率
			'0':[0.5,0.3,0.1,0,0.1],		#春：【无天气5，沙3，雨1，雾0，雪1】
			'1':[0.5,0.1,0.3,0.1,0],		#夏：【无天气5，沙1，雨3，雾1，雪0】
			'2':[0.5,0,0.1,0.3,0.1],		#秋：【无天气5，沙0，雨1，雾3，雪1】
			'3':[0.5,0.1,0,0.1,0.3],		#冬：【无天气5，沙1，雨0，雾1，雪3】
		},
		'spread_list':[0],		#扩散黑名单，0无天气，不会扩散
	},
	
		#每个偏移值执行以下内容：
		#1：清空当前所有城池的天气
		#2：按照'pathogen'内cityType的降序，根据当前季节进行随机天气生成
		#2.5：从当前已有的天气原体，开始向外完成扩散，被扩散的cityType必须小于原体的cityType，若被扩散城池已有天气，则不扩散
		#3：依次执行上面两个步骤，直至完成'pathogen'内的所有内容
		#4:已经生成天气记在每个城池上，前端需要读取相关内容








###################战计配置#################################

  "scheme_merge":4,#4次合服后开启

  'scheme_upgrade':[

      [1,4,15,40,80,150,250,480],[0,4,15,40,80,150,250,480],[0,0,15,40,80,150,250,480],[0,0,0,40,80,150,250,480]

      #战计不同稀有度的升星消耗，数组对应稀有度1，2，3，4，数组内对应1-8星的消耗。当消耗为0时，对应稀有度下不存在该星级的战计，初始星级从首个不为0的消耗开始
  ],

  'scheme_limit':4,#定计的星级限制

  'scheme_hero_len':10,#定计的英雄位置数量

  'scheme_level_max':['building027',3],  #建筑等级，额外等级，使用当前建筑等级和额外等级加与scheme_upgrade配置中的数组上限取最小值获得当前星级的上限


###################转生配置#################################

  #转生次数对应的额外可学习英雄技,从0转开始
  'revived_hero_skill':[0,0,0,1,1,1],
  #转生次数对应的可分配四维点,从0转开始
  'revived_assign':[0,0,2,2,2,6],
  #转生每次洗点消耗的设定
  'revived_wash':['item2007',3],#每洗1点消耗的道具和数量


################战阶配置#######################################

'power_level':{


 'switch':1, #功能开关，合服次数大于等于该数字的可以看到功能，-1关闭
 
 'power_level_limit':[0,5,10,15,20,25,35],#每次合服的战阶上限，当合服次数大于数组长度时，取最后一位

 'power_level_data':[  #power战力 #reward奖励 effect效果 #fief_produce封地 #estate_produce产业粮食	#'army_stock军营最大存量	#army_add兵种单次训练量	#army_rate练兵速度	#army_consume训练消耗资源

      {'power':1000000,'reward':{'coin':200},'effect':{'fief_produce':['food',0.01],'estate_produce':['3',0.01],'army_stock':['all',0.01],'army_add':['all',0.01],'army_rate':['all',0.02],},},
      {'power':2000000,'reward':{'coin':200},'effect':{'fief_produce':['food',0.02],'estate_produce':['3',0.02],'army_stock':['all',0.02],'army_add':['all',0.02],'army_rate':['all',0.04],},},
      {'power':3000000,'reward':{'coin':200},'effect':{'fief_produce':['food',0.03],'estate_produce':['3',0.03],'army_stock':['all',0.03],'army_add':['all',0.03],'army_rate':['all',0.06],},},
      {'power':4000000,'reward':{'coin':200},'effect':{'fief_produce':['food',0.04],'estate_produce':['3',0.04],'army_stock':['all',0.04],'army_add':['all',0.04],'army_rate':['all',0.08],},},
      {'power':5000000,'reward':{'coin':200},'effect':{'fief_produce':['food',0.05],'estate_produce':['3',0.05],'army_stock':['all',0.05],'army_add':['all',0.05],'army_rate':['all',0.1],},},
      {'power':6000000,'reward':{'coin':400},'effect':{'fief_produce':['food',0.06],'estate_produce':['3',0.06],'army_stock':['all',0.06],'army_add':['all',0.06],'army_rate':['all',0.12],},},
      {'power':7000000,'reward':{'coin':400},'effect':{'fief_produce':['food',0.07],'estate_produce':['3',0.07],'army_stock':['all',0.07],'army_add':['all',0.07],'army_rate':['all',0.14],},},
      {'power':8000000,'reward':{'coin':400},'effect':{'fief_produce':['food',0.08],'estate_produce':['3',0.08],'army_stock':['all',0.08],'army_add':['all',0.08],'army_rate':['all',0.16],},},
      {'power':9000000,'reward':{'coin':400},'effect':{'fief_produce':['food',0.09],'estate_produce':['3',0.09],'army_stock':['all',0.09],'army_add':['all',0.09],'army_rate':['all',0.18],},},
      {'power':10000000,'reward':{'coin':400},'effect':{'fief_produce':['food',0.1],'estate_produce':['3',0.1],'army_stock':['all',0.1],'army_add':['all',0.1],'army_rate':['all',0.2],},},
      {'power':12000000,'reward':{'coin':600},'effect':{'fief_produce':['food',0.11],'estate_produce':['3',0.11],'army_stock':['all',0.11],'army_add':['all',0.11],'army_rate':['all',0.3],},},
      {'power':14000000,'reward':{'coin':600},'effect':{'fief_produce':['food',0.12],'estate_produce':['3',0.12],'army_stock':['all',0.12],'army_add':['all',0.12],'army_rate':['all',0.4],},},
      {'power':16000000,'reward':{'coin':600},'effect':{'fief_produce':['food',0.13],'estate_produce':['3',0.13],'army_stock':['all',0.13],'army_add':['all',0.13],'army_rate':['all',0.5],},},
      {'power':18000000,'reward':{'coin':600},'effect':{'fief_produce':['food',0.14],'estate_produce':['3',0.14],'army_stock':['all',0.14],'army_add':['all',0.14],'army_rate':['all',0.6],},},
      {'power':20000000,'reward':{'coin':600},'effect':{'fief_produce':['food',0.15],'estate_produce':['3',0.15],'army_stock':['all',0.15],'army_add':['all',0.15],'army_rate':['all',0.8],},},
      {'power':24000000,'reward':{'coin':800},'effect':{'fief_produce':['food',0.2],'estate_produce':['3',0.2],'army_stock':['all',0.16],'army_add':['all',0.6],'army_rate':['all',0.9],},},
      {'power':28000000,'reward':{'coin':800},'effect':{'fief_produce':['food',0.25],'estate_produce':['3',0.25],'army_stock':['all',0.17],'army_add':['all',1.2],'army_rate':['all',1],},},
      {'power':32000000,'reward':{'coin':800},'effect':{'fief_produce':['food',0.3],'estate_produce':['3',0.3],'army_stock':['all',0.18],'army_add':['all',1.8],'army_rate':['all',1.1],},},
      {'power':36000000,'reward':{'coin':800},'effect':{'fief_produce':['food',0.35],'estate_produce':['3',0.35],'army_stock':['all',0.19],'army_add':['all',2.4],'army_rate':['all',1.2],},},
      {'power':40000000,'reward':{'coin':800},'effect':{'fief_produce':['food',0.4],'estate_produce':['3',0.4],'army_stock':['all',0.2],'army_add':['all',3],'army_rate':['all',1.4],},},
      {'power':48000000,'reward':{'coin':1000},'effect':{'fief_produce':['food',0.45],'estate_produce':['3',0.45],'army_stock':['all',0.25],'army_add':['all',3.6],'army_rate':['all',1.5],'army_consume':[0.1],},},
      {'power':56000000,'reward':{'coin':1000},'effect':{'fief_produce':['food',0.5],'estate_produce':['3',0.5],'army_stock':['all',0.3],'army_add':['all',4.2],'army_rate':['all',1.6],'army_consume':[0.1],},},
      {'power':64000000,'reward':{'coin':1000},'effect':{'fief_produce':['food',0.6],'estate_produce':['3',0.6],'army_stock':['all',0.35],'army_add':['all',4.8],'army_rate':['all',1.7],'army_consume':[0.1],},},
      {'power':72000000,'reward':{'coin':1000},'effect':{'fief_produce':['food',0.7],'estate_produce':['3',0.7],'army_stock':['all',0.4],'army_add':['all',5.4],'army_rate':['all',1.8],'army_consume':[0.1],},},
      {'power':80000000,'reward':{'coin':1000},'effect':{'fief_produce':['food',0.8],'estate_produce':['3',0.8],'army_stock':['all',0.5],'army_add':['all',6],'army_rate':['all',2],'army_consume':[0.1],},},
      {'power':90000000,'reward':{'coin':1200},'effect':{'fief_produce':['food',0.9],'estate_produce':['3',0.9],'army_stock':['all',0.6],'army_add':['all',6.6],'army_rate':['all',2.1],'army_consume':[0.1],},},
      {'power':100000000,'reward':{'coin':1200},'effect':{'fief_produce':['food',1],'estate_produce':['3',1],'army_stock':['all',0.7],'army_add':['all',7.2],'army_rate':['all',2.2],'army_consume':[0.1],},},
      {'power':115000000,'reward':{'coin':1200},'effect':{'fief_produce':['food',1.1],'estate_produce':['3',1.1],'army_stock':['all',0.8],'army_add':['all',7.8],'army_rate':['all',2.3],'army_consume':[0.1],},},
      {'power':135000000,'reward':{'coin':1200},'effect':{'fief_produce':['food',1.2],'estate_produce':['3',1.2],'army_stock':['all',0.9],'army_add':['all',8.4],'army_rate':['all',2.4],'army_consume':[0.1],},},
      {'power':150000000,'reward':{'coin':1200},'effect':{'fief_produce':['food',1.3],'estate_produce':['3',1.3],'army_stock':['all',1],'army_add':['all',9],'army_rate':['all',2.5],'army_consume':[0.1],},},
      {'power':180000000,'reward':{'coin':1500},'effect':{'fief_produce':['food',1.4],'estate_produce':['3',1.4],'army_stock':['all',1.1],'army_add':['all',9.6],'army_rate':['all',2.6],'army_consume':[0.1],},},
      {'power':210000000,'reward':{'coin':1500},'effect':{'fief_produce':['food',1.5],'estate_produce':['3',1.5],'army_stock':['all',1.2],'army_add':['all',10.2],'army_rate':['all',2.7],'army_consume':[0.1],},},
      {'power':240000000,'reward':{'coin':1500},'effect':{'fief_produce':['food',1.6],'estate_produce':['3',1.6],'army_stock':['all',1.3],'army_add':['all',10.8],'army_rate':['all',2.8],'army_consume':[0.1],},},
      {'power':270000000,'reward':{'coin':1500},'effect':{'fief_produce':['food',1.7],'estate_produce':['3',1.7],'army_stock':['all',1.4],'army_add':['all',11.4],'army_rate':['all',2.9],'army_consume':[0.1],},},
      {'power':300000000,'reward':{'coin':1500},'effect':{'fief_produce':['food',1.8],'estate_produce':['3',1.8],'army_stock':['all',1.5],'army_add':['all',12],'army_rate':['all',3],'army_consume':[0.1],},},

 









],



},

        # 神将拍卖限制配置
        'auction_limit': {
            'enable': 1,                 # 功能开关 0关闭/1开启
            'day_limits': [              # 按开服天数的动态限制配置，按天数从小到大排序
                {
                    'day': 1,            # 开服第1天开始
                    'building_level': 18, # 府邸等级要求
                    'hero_power': 300000, # 最强英雄战力要求
                },
                {
                    'day': 2,            # 开服第2天开始
                    'building_level': 21, # 府邸等级要求
                    'hero_power': 500000, # 最强英雄战力要求
                },
                {
                    'day': 6,            # 开服第6天开始
                    'building_level': 24, # 府邸等级要求
                    'hero_power': 800000, # 最强英雄战力要求
                },
            ],
        },

}