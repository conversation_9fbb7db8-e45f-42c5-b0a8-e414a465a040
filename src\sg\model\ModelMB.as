package sg.model {

    import sg.cfg.ConfigServer;
    import sg.manager.ModelManager;

    public class Model<PERSON> extends ModelBase {
        public function isCanMBPay(pids:String):Boolean {
            var cfg:Object = ConfigServer.pay_config[pids];
            if (ModelManager.instance.modelUser.ucoin >= cfg[0]) {
                return true;
            }
            return false;
        }

        public function isCanLCoinPay(pids:String):Boolean {
            var cfg:Object = ConfigServer.pay_config[pids];
            if (ModelManager.instance.modelUser.lcoin >= cfg[0]) {
                return true;
            }
            return false;
        }
    }
}
