{% extends 'admin/base.html' %}
{% block content %}
<script src="{{settings.MEDIA_URL}}/js/jquery-1.12.3.min.js" type="text/javascript"></script>
<script type='text/javascript' src='{{settings.BASE_URL}}/static/js/jquery-ui-1.12.1/jquery-ui.min.js'></script>
<link rel="stylesheet" href="{{settings.BASE_URL}}/static/js/jquery-ui-1.12.1/jquery-ui.min.css" type="text/css"/>
<script type='text/javascript' src='{{settings.MEDIA_URL}}/js/reward_content.js?v=22'></script>
<script type='text/javascript'>

    function chk(){
        var rewardType = $('input[name="reward_type"]:checked').val();
        
        if (rewardType == 'manual') {
            var userObj = document.getElementById('uids');
            if (userObj.value.length == 0){
                alert('请填写用户UID');
                userObj.focus();
                return false;
            }
        } else if (rewardType == 'zone') {
            var zoneList = document.getElementById('zone_list');
            if (zoneList.value.length == 0){
                alert('请填写区服列表');
                zoneList.focus();
                return false;
            }
        }
        
        if ($('#name_{{nafo_list.0.0}}').val() == ''){
            alert('{{nafo_list.0.0}}标题不能为空');
            $('#name_{{nafo_list.0.0}}').focus();
            return false;
        }
        if ($('#info_{{nafo_list.0.0}}').val() == ''){
            alert('{{nafo_list.0.0}}正文描述不能为空');
            $('#info_{{nafo_list.0.0}}').focus();
            return false;
        }

        {%for item in input_list%}
        if($('#{{item.0}}').val()!=''){ 
            if(parseInt($('#{{item.0}}').val()) != $('#{{item.0}}').val()){
                alert('请正确填写{{item.1}}数量');
                $('#{{item.0}}').focus();
                return false;
            }
            if(parseInt($('#{{item.0}}').val()) > parseInt({{item.2}})){
                alert('{{item.1}}数量不能超过{{item.2}}');
                $('#{{item.0}}').focus();
                return false;
            }
        }
        {%endfor%}

        return true;
    }

    function toggleRewardType() {
        var rewardType = $('input[name="reward_type"]:checked').val();
        
        if (rewardType == 'manual') {
            $('#manual_content').show();
            $('#zone_content').hide();
            $('#filter_content').hide();
        } else if (rewardType == 'zone') {
            $('#manual_content').hide();
            $('#zone_content').show();
            $('#filter_content').show();
        } else if (rewardType == 'all') {
            $('#manual_content').hide();
            $('#zone_content').hide();
            $('#filter_content').show();
        }
    }

    function toggleFilters() {
        var enableFilters = $('#enable_filters').is(':checked');
        if (enableFilters) {
            $('#filter_options').show();
        } else {
            $('#filter_options').hide();
        }
    }

    $(document).ready(function() {
        toggleRewardType();
        toggleFilters();
    });

</script>
<h2>高级邮件发奖</h2>
<br/>
<form action="{{settings.BASE_URL}}/admin/reward/set_reward_to_user_advanced/"
    method="post" onsubmit="return chk();">
    <input type="submit" value="发送"/>

<table width="100%">
    <!-- 发奖类型选择 -->
    <tr>
        <td width="10%">发奖类型</td>
        <td>
            <input type="radio" name="reward_type" value="manual" checked onclick="toggleRewardType()"/> 手动输入UID
            <input type="radio" name="reward_type" value="zone" onclick="toggleRewardType()"/> 指定区服发奖
            <input type="radio" name="reward_type" value="all" onclick="toggleRewardType()"/> 全服发奖
        </td>
    </tr>
    
    <!-- 手动输入UID -->
    <tr id="manual_content">
        <td width="10%">用户UID</td>
        <td>
            <textarea id="uids" name="uids" style="width: 70%; height: 50px">{{ uids }}</textarea>
            (多用户用英文逗号分割，<font color="red">填写用户长uid</font>)
        </td>
    </tr>
    
    <!-- 区服选择 -->
    <tr id="zone_content" style="display:none;">
        <td width="10%">区服列表</td>
        <td>
            <textarea id="zone_list" name="zone_list" style="width: 70%; height: 50px"></textarea>
            (多区服用英文逗号分割，如：1,2,3)
            <br/>
            可选区服：
            {%for zone in zone_list%}
                <span style="margin-right: 10px;">{{zone.0}}({{zone.1}})</span>
            {%endfor%}
        </td>
    </tr>
    
    <!-- 条件过滤 -->
    <tr id="filter_content" style="display:none;">
        <td width="10%" style="vertical-align: top;">条件过滤</td>
        <td>
            <input type="checkbox" id="enable_filters" name="enable_filters" value="1" onclick="toggleFilters()"/> 启用条件过滤
            <div id="filter_options" style="display:none; margin-top: 10px; padding: 15px; border: 1px solid #ddd; background-color: #f9f9f9;">
                <table style="width: 100%;">
                    <tr>
                        <td style="width: 120px; padding: 5px;">府邸等级：</td>
                        <td style="padding: 5px;">
                            <input type="number" name="min_building_level" value="1" min="1" max="999" style="width: 60px;"/>
                            到
                            <input type="number" name="max_building_level" value="999" min="1" max="999" style="width: 60px;"/>
                            <span style="color: #666; margin-left: 10px;">（建筑001等级范围）</span>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 5px;">注册天数：</td>
                        <td style="padding: 5px;">
                            <input type="number" name="min_register_days" value="0" min="0" max="99999" style="width: 60px;"/>
                            到
                            <input type="number" name="max_register_days" value="99999" min="0" max="99999" style="width: 60px;"/>
                            <span style="color: #666; margin-left: 10px;">（注册多少天的玩家）</span>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 5px;">战力范围：</td>
                        <td style="padding: 5px;">
                            <input type="number" name="min_power" value="0" min="0" max="99999999" style="width: 80px;"/>
                            到
                            <input type="number" name="max_power" value="99999999" min="0" max="99999999" style="width: 80px;"/>
                            <span style="color: #666; margin-left: 10px;">（玩家战力范围）</span>
                        </td>
                    </tr>
                </table>
                <div style="margin-top: 10px; padding: 8px; background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404;">
                    <strong>注意：</strong>启用条件过滤会显著增加处理时间，因为需要逐个检查用户信息。建议在用户较少的区服使用。
                </div>
            </div>
        </td>
    </tr>
    
    <tr><td colspan="2"><hr/></td></tr>
    
    <!-- 邮件标题和内容 -->
    {%for item in nafo_list%}
    <tr>
        <td>{{item.0}}标题</td>
        <td>
            <input id="name_{{item.0}}" name="name_{{item.0}}" type="text" style="width: 70%;" value="{{name_info.item.0.0}}"/>
        </td>
    </tr>
    <tr>
        <td>{{item.0}}正文</td>
        <td>
            <textarea id="info_{{item.0}}" name="info_{{item.0}}" style="width: 70%; height: 50px;">{{name_info.item.0.1}}</textarea>
        </td>
    </tr>
    {%endfor%}
    
    <tr><td colspan="2"><hr/></td></tr>
    
    <!-- 奖励内容 -->
    <tr>
        <td>奖励内容</td>
        <td>
            <!-- 基础奖励 -->
            {%for item in input_list%}
            <div style="margin-bottom: 5px;">
                {{item.1}}：<input id="{{item.0}}" name="{{item.0}}" type="text" style="width: 100px;" value=""/> (最大{{item.2}})
            </div>
            {%endfor%}

            <!-- 道具奖励 -->
            <div style="margin-top: 10px;">
                <strong>道具奖励：</strong><br/>
                <textarea name="item_rewards" style="width: 70%; height: 60px;" placeholder="格式：item001:10,item002:5 (道具ID:数量,道具ID:数量)"></textarea>
            </div>

            <!-- 装备奖励 -->
            <div style="margin-top: 10px;">
                <strong>装备奖励：</strong><br/>
                <textarea name="equip_rewards" style="width: 70%; height: 60px;" placeholder="格式：E_1001_5:1,E_1002_4:2 (装备ID:数量,装备ID:数量)"></textarea>
            </div>

            <!-- 英雄奖励 -->
            <div style="margin-top: 10px;">
                <strong>英雄奖励：</strong><br/>
                <textarea name="hero_rewards" style="width: 70%; height: 60px;" placeholder="格式：H001:1,H002:1 (英雄ID:数量,英雄ID:数量)"></textarea>
            </div>
        </td>
    </tr>
</table>

</form>

{% endblock %}
