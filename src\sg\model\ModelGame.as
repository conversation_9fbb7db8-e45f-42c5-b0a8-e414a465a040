package sg.model {
    import sg.manager.ColorManager;
    import sg.map.utils.TestUtils;
    import sg.map.utils.Vector2D;
    import sg.model.ModelBase;
    import laya.utils.Utils;
    import sg.manager.ModelManager;
    import sg.cfg.ConfigServer;
    import sg.scene.constant.EventConstant;
    import sg.scene.view.TestButton;
    import sg.utils.Tools;
    import sg.net.NetSocket;
    import laya.utils.Handler;
    import sg.net.NetPackage;
    import sg.cfg.ConfigClass;
    import sg.manager.ViewManager;
    import laya.events.Event;
    import sg.net.NetMethodCfg;
    import laya.ui.Button;
    import sg.manager.AssetsManager;
    import sg.view.effect.BuildingFuncOpen;
    import sg.map.model.MapModel;
    import sg.view.map.ViewEstateQuickly;
    import sg.view.map.ViewEventTalk;
    import sg.view.map.ViewCreditResult;
    import sg.activities.model.ModelFreeBuy;
    import laya.display.Sprite;
    import laya.ui.Image;
    import sg.guide.model.ModelGuide;
    import sg.boundFor.GotoManager;
    import sg.activities.model.ModelHappy;
    import sg.manager.QueueManager;
    import laya.maths.MathUtil;
    import sg.utils.StringUtil;
    import sg.cfg.ConfigApp;
    import sg.utils.SaveLocal;
    import sg.net.NetHttp;
    import sg.crossService.CrossServiceFacade;
    import sg.crossService.model.CrossServiceUserModel;
    import sg.utils.ServerDate;
    import sg.view.levelupGift.LevelupGiftModelManager;
    import sg.festival.model.ModelFestival;
    import sg.view.init.ViewHeroTalk2;
    import sg.model.ModelNewMilepost;
    import sg.view.fight.gwent.C_Gwent;
    import sg.kunlun.model.ModelKunlunSale;
    import sg.view.fight.champion_sg_new.C_Champion;
    import sg.interior.model.ModelCityControl;
    import sg.view.task.ng_task.C_NGTask;
    import sg.puppet.model.ModelCountryStoreroom;
    import sg.puppet.PuppetUtils;

    /**
     * ...
     * <AUTHOR>
    public class ModelGame extends ModelBase {

        //
        public static const EVENT_NEW_DAY_COM_ON:String = "event_new_day_com_on"; //新得一天,通告一次
        public static const EVENT_SERVER_SELECT_CHANGE:String = "event_server_select_change";
        //
        public static const EVENT_HERO_LV_EXP_CHANGE:String = "event_hero_lv_exp_change";
        public static const EVENT_HERO_SKILL_CHANGE:String = "event_hero_skill_change";
        public static const EVENT_HERO_TROOP_EDIT_UI_CHANGE:String = "event_hero_troop_edit_ui_change";
        //
        public static const EVENT_PK_TIMES_CHANGE:String = "event_pk_times_change"; //
        public static const EVENT_OFFICE_RIGHT_CHANGE:String = "event_office_right_change"; //
        public static const EVENT_CHAMPION_BET_CHANGE:String = "event_champion_bet_change"; //比武大会,押注状态/

        public static const EVENT_TASK_RED:String = "event_task_red"; //任务red
        public static const EVENT_TASK_WORK_CHANGE:String = "event_task_work_change"; //政务,状态改变
        public static const EVENT_TASK_WORK_GET_OR_DEL:String = "event_task_work_get_or_del"; //政务,状态改变2
        public static const EVENT_TASK_MAIN_CLOSE_VIEW:String = "event_task_main_close_view"; //任务主界面关闭
        //
        public static const EVENT_GO_MAP:String = "event_go_map"; //跳转大地图
        public static const EVENT_GO_HOME:String = "event_go_home"; //跳转封地
        public static const EVENT_REAL_NAME_CHECK_TIRED_TIME:String = "event_real_name_check_tired_time"; //实名提示时间
        //异族入侵
        public static const EVENT_PK_NPC_CHECK_MODEL:String = "event_pk_npc_check_model"; //异族入侵检查数据
        public static const EVENT_ALIEN_FIGHT_START:String = "event_alien_fight_start"; //异族入侵开始战斗
        public static const EVENT_ALIEN_FIGHT_END:String = "event_alien_fight_end"; //异族入侵战斗 结束
        public static const EVENT_CAPTAIN_FIGHT_START:String = "event_captain_fight_start"; //异族入侵战斗 结束
        public static const EVENT_CAPTAIN_FIGHT_END:String = "event_captain_fight_end"; //异族入侵战斗 结束

        public static const EVENT_UPDAET_BUG_MSG:String = "event_update_bug_msg";

        public static const EVENT_ADD_ESTATE:String = "event_add_estate";
        public static const EVENT_REMOVE_ESTATE:String = "event_remove_estate";
        public static const EVENT_FIGHT_LOG_CHANGE:String = "event_fight_log_change"; //战报有变化
        public static const EVENT_CREDIT_CHANGE:String = "event_CREDIT_change"; //战功奖励变化
        public static const EVENT_BUFFS_ORDER_3_4_CHANGE:String = "event_buffs_order_3_4_change"; //攻城令/守城令
        public static const EVENT_BUFFS_ORDER_5_CHANGE:String = "event_buffs_order_5_change"; //
        public static const EVENT_BUFFS_ORDER_CORPS_CHANGE:String = "event_buffs_order_corps_change"; //
        public static const EVENT_BUFFS_EXPEDITION_CHANGE:String = "event_buffs_expedition_change"; //
        public static const EVENT_STAGE_LOCK_UNLOCK:String = "event_stage_lock_unlock"; //全局屏蔽
        public static const EVENT_CHECK_NPC_INFO:String = "event_check_npc_info"; //检查斥候情报红点

        public static const EVENT_MAYOR_UDPATE:String = "event_mayor_update"; //更新太守。

        //
        public static const EVENT_PAY_END:String = "event_pay_end"; //充值结束
        public static const EVENT_PAY_LIST_UPDATE:String = "event_pay_list_update";
        // 天下大势改变
        public static const EVENT_INVADE_CHANGE:String = "event_invade_change";

        public static const EVENT_SHOW_BALLISTA_MSG:String = "event_show_ballista_msg";

        public static const EVENT_CLOSE_EQUIP_MAIN:String = "event_close_equip_main";
        public static const EVENT_UPDATE_EQUIP_MAIN:String = "event_update_equip_main";

        //朝廷密旨收到服务器推送时派发
        public static const EVENT_NEW_TASK_PUSH:String = "event_new_task_push";

        public static const EVENT_CLICK_ARENA_CLIP:String = "event_click_arena_clip";

        public static const EVENT_EQUIP_ENHANCE_ING:String = "event_equip_enhance_ing"; //正在强化

        public static const EVENT_SHOW_CHAT:String = "event_show_chat";
        public static const EVENT_HIDE_CHAT:String = "event_hide_chat";

        public static const EVENT_UPDATE_NEW_RANK_BTN:String = "event_update_new_rank_btn";

        public static const EVENT_CLICK_SWITCH_BTN:String = "event_click_switch_btn";
        //
        public static const map_troop_status_str:Array = [Tools.getMsgById("_public161"), // 空闲中 0
            Tools.getMsgById("_public162"), // 行军中 1
            Tools.getMsgById("_public163"), // 撤回中 2
            Tools.getMsgById("_public164"), // 野战中 3
            Tools.getMsgById("_duplicate043"), // 重伤中 4
            ];
        //
        public var inHome:Boolean = false; // 当前是否处于封地
        public var inMap:Boolean = true; // 当前是否处于大地图
        public var gameTimer:Number = 0;
        public var gameTipsTimer:Number = -1;
        public var gameTipsTimerDes:Number = 10;
        public var creatNewTroopID:Object = {};

        public var comChatVisible:Boolean = false;

        public function ModelGame() {
            QueueManager.instance.on(QueueManager.EVENT_CLOSE_PANEL, this, queueCallBack);
        }

        /**
         * 初始化一些监听
         */
        public function initRegisterHandler():void {
            getRecords();
            getCoinByOffice();
            buildCityBuildHandler();
            chatHandler();
            creditSettleHandler();

            clubAlienFight();
            clubAlienChange();
            clubRedBagUpdate();
            bugMSGHandler();
            updateUserHandler();
            pushMsgHandler();

            mailGiftMsg();
            freeBuyMsg();
            getEstateMSG();
            pushTask();
            pushLevelupGift();
            pushEffort();
            pushAuction();
            pushNewTask();
            pushHonour();
            pushFreezeDo();
            pushAsk();
            pushAnswerLog();
            getFefreshGwent();
            getFefreshGodSale();
            getFefreshGroup();
            getFefreshNGTask();
            getCountryStoreroomMSG();
            getUserStoreroomMSG();

            on(EVENT_GO_MAP, this, _onGoMap);
        }

        private var mFirstGoToMap:Boolean = true;
        private function _onGoMap():void {
            mFirstGoToMap || checkPKnpcCaptainLocal(true); // 第一次进地图不执行
            mFirstGoToMap = false;
        }

        private function queueCallBack(vid:String):void {
            QueueManager.instance.showNext(vid);
        }

        /**
         * 产业倒计时，每秒调用
         */
        public function updateMyEstateStatus():void {
            var b:Boolean = false;
            for (var i:int = 0; i < ModelManager.instance.modelUser.estate.length; i++) {
                var obj:Object = ModelManager.instance.modelUser.estate[i];
                var estate:ModelEstate = ModelEstate.myCountryEstates[obj.city_id + "_" + obj.estate_index];
                if (estate) {
                    if (estate.next_harvest_time != -1 && ConfigServer.getServerTimer() > estate.next_harvest_time) {
                        estate.event(ModelEstate.EVENT_ESTATE_UPDATE); //可收割推送
                        estate.getNextHarvestTime();
                    }

                    var hero:ModelMapHero = estate.estateHero;
                    if (hero && !hero["sendEvent"] && hero.getTime() <= ConfigServer.getServerTimer()) {
                        estate.event(ModelEstate.EVENT_ESTATE_UPDATE); //完成任务推送
                        hero["sendEvent"] = true;
                        b = true;
                    }
                }

            }
            if (b) {
                ModelManager.instance.modelUser.setEstateManagerArr();
                ModelManager.instance.modelUser.event(ModelUser.EVENT_USER_UPDATE, {"user": {"estate": ""}}); //通知红点刷新
            }
        }

        /**
         * 拜访倒计时，每秒调用
         */
        public function updateMyVisitStatus():void {
            var b:Boolean = false;
            for (var s:String in ModelVisit.visitModels) {
                var visit:ModelVisit = ModelVisit.visitModels[s];
                if (!visit["sendEvent"] && visit.visitHero) {
                    var n1:Number = ConfigServer.getServerTimer();
                    var n2:Number = visit.visitHero.getTime();
                    if (n1 >= n2) {
                        ModelVisit.updateData(s);
                        visit["sendEvent"] = true;
                        b = true;
                    }
                }
            }
            if (b) {
                ModelManager.instance.modelUser.setEstateManagerArr();
                ModelManager.instance.modelUser.event(ModelUser.EVENT_USER_UPDATE, {"user": {"visit": ""}}); //通知红点刷新
            }
        }

        public function updateNewVisit():void { //新的一天刷新
            ////Trace.log("新的一天了！更新所有visit");
            for (var s:String in ModelVisit.visitModels) {
                var visit:ModelVisit = ModelVisit.visitModels[s];
                visit.clear();
            }
            getCityVisit();
        }

        public function getModelCityBuild(cid:String, bid:String):ModelCityBuild {
            if (ModelCityBuild.cityBuildModels) {
                if (ModelCityBuild.cityBuildModels[cid]) {
                    if (ModelCityBuild.cityBuildModels[cid][bid]) {
                        return ModelCityBuild.cityBuildModels[cid][bid];
                    }
                }
            }
            return null;
        }

        /**
         * 城市建设倒计时，每秒调用
         */
        public function updateMyCityBuildStatus():void {
            var b:Boolean = false;
            for (var s:String in ModelCityBuild.cityBuildModels) {
                var obj:Object = ModelCityBuild.cityBuildModels[s];
                for (var ss:String in obj) {
                    var cityBuild:ModelCityBuild = (obj[ss] as ModelCityBuild);
                    if (!cityBuild["sendEvent"] && cityBuild.cityBuildHero) {
                        var n1:Number = ConfigServer.getServerTimer();
                        var n2:Number = cityBuild.cityBuildHero.getTime();
                        if (n1 >= n2) {
                            cityBuild["sendEvent"] = 1;
                            cityBuild.event(ModelCityBuild.EVENT_UPDATE_CITY_BUILD);
                            b = true;
                        }
                    }
                }
            }
            if (b) {
                ModelManager.instance.modelUser.setEstateManagerArr();
                ModelManager.instance.modelUser.event(ModelUser.EVENT_USER_UPDATE, {"user": {"city_build": ""}}); //通知红点刷新
            }
        }

        /**
         * 初始化民情数据（先调接口）
         */
        public function initFtask():void {
            ModelFTask.ftaskModels = {};
            for (var s:String in ModelManager.instance.modelUser.ftask) {
                if (ConfigServer.city[s] && ConfigServer.city[s].pctask_id) { // && ModelOfficial.checkCityIsMyCountry(s)) {
                    var ftask:Array = ModelManager.instance.modelUser.ftask[s];
                    if (ftask[0] == -1) {
                        // Trace.log(s+"已完成民情 不初始化了");
                    } else {
                        var ft:ModelFTask = new ModelFTask(s);
                        if (ModelOfficial.checkCityIsMyCountry(s)) {
                            ModelFTask.ftaskModels[s] = ft;
                        } else {
                            if (ModelCityControl.isOpen()) {
                                if (ftask[3]) {
                                    ModelFTask.ftaskModels[s] = ft;
                                }
                            }
                        }
                    }
                }
            }
            this.event(ModelFTask.EVENT_INIT_FTASK);
        }

        public function addFtask(cid:String):void {
            if (!ModelFTask.ftaskModels.hasOwnProperty(cid)) {
                if (ModelManager.instance.modelUser.ftask[cid][0] != -1) {
                    var md:ModelFTask = getModelFtask(cid);
                    ModelFTask.ftaskModels[cid] = md;
                    this.event(ModelFTask.EVENT_ADD_FTASK, md);
                }
            } else {

            }
        }

        public function removeFtask(cid:String):void {
            if (ModelFTask.ftaskModels.hasOwnProperty(cid)) {
                var md:ModelFTask = ModelFTask.ftaskModels[cid];
                delete ModelFTask.ftaskModels[cid];
                md.event(ModelFTask.EVENT_REMOVE_FTASK, md);
            }
        }

        /**
         * 跳过新手引导检查已完成的民情 并移除
         */
        public function checkFtask():void {
            var obj:Object = ModelManager.instance.modelUser.ftask;
            for (var k:String in ModelFTask.ftaskModels) {
                if (obj[k] && obj[k][0] == -1) {
                    removeFtask(k);
                }
            }
        }



        /*
           ##################################   获取model    ####################################
         */

        /**
         * 获取 产业 model
         */
        public function getModelEstate(cid:String, index:int):ModelEstate {
            var key:String = cid + "_" + index;
            if (!ModelEstate.estateModels.hasOwnProperty(key)) {
                if (ConfigServer.city[cid].estate[index] != null) {
                    ModelEstate.estateModels[key] = new ModelEstate(cid, index);
                    if (ModelOfficial.checkCityIsMyCountry(cid)) {
                        ModelEstate.myCountryEstates[key] = ModelEstate.estateModels[key];
                    } else {
                        var user_estate:Object = ModelManager.instance.modelUser.estate;
                        for (var j:int = 0; j < user_estate.length; j++) {
                            var obj_estate:Object = user_estate[j];
                            if (obj_estate.city_id == cid && obj_estate.estate_index == index) {
                                ModelEstate.myCountryEstates[key] = ModelEstate.estateModels[key];
                                break;
                            }
                        }
                    }
                } else {

                }

            }
            return ModelEstate.estateModels[key] ? ModelEstate.estateModels[key] : null;
        }

        /**
         * 获得 接管 model
         */
        public function getModelCityControl(cid:*):ModelCityControl {
            if (ModelFTask.ftaskModels && ModelFTask.ftaskModels[cid]) {
                var md:ModelFTask = ModelFTask.ftaskModels[cid] as ModelFTask;
                if (md) {
                    return md.cityControl;
                }
            }
            return null;
        }

        /**
         * 获得 民情 model
         */
        public function getModelFtask(cid:String):ModelFTask {
            var obj:Object = ModelManager.instance.modelUser.ftask;
            if (obj && obj[cid]) {
                if (!ModelFTask.ftaskModels.hasOwnProperty(cid)) {
                    ModelFTask.ftaskModels[cid] = new ModelFTask(cid);
                }
                return ModelFTask.ftaskModels[cid];
            }
            return null;
        }

        /**
         * 获得 拜访 model
         */
        public function getModelVisit(cid:String):ModelVisit {
            return ModelVisit.visitModels[cid];

        }

        /**
         * 获取 英雄 model
         */
        public function getModelHero(key:String):ModelHero {
            if (!key) {
                return null;
            }
            if (!ModelHero.heroModels.hasOwnProperty(key)) {
                ModelHero.heroModels[key] = new ModelHero();
                (ModelHero.heroModels[key] as ModelHero).initData(key, ConfigServer.hero[key]);
            }
            return ModelHero.heroModels[key] as ModelHero;
        }

        /**
         * 获取 英雄技能 model
         */
        public function getModelSkill(key:String):ModelSkill {
            if (!key) {
                return null;
            }
            return ModelSkill.getModel(key);
        }

        /**
         * 获取 商店 model
         */
        public function getModelShop(key:String):ModelShop {
            if (!key) {
                return null;
            }
            if (!ModelShop.shopModels.hasOwnProperty(key)) {
                ModelShop.shopModels[key] = new ModelShop();
                (ModelShop.shopModels[key] as ModelShop).initData(key);
            }
            return ModelShop.shopModels[key] as ModelShop;
        }



        /**
         * 获取 装备宝物 model
         */
        public function getModelEquip(key:String):ModelEquip {
            if (!key) {
                return null;
            }
            if (!ModelEquip.equipModels.hasOwnProperty(key)) {
                ModelEquip.equipModels[key] = new ModelEquip();
                (ModelEquip.equipModels[key] as ModelEquip).initData(key, ModelEquip.getConfig(key));
            }
            return ModelEquip.equipModels[key] as ModelEquip;
        }

        /**
         * 获取 星辰 model
         */
        public function getModelRune(key:String):ModelRune {
            if (!key) {
                return null;
            }
            if (!ModelRune.runeModels.hasOwnProperty(key)) {
                ModelRune.runeModels[key] = new ModelRune();
                (ModelRune.runeModels[key] as ModelRune).initData(key, ModelRune.getConfig(key));
            }
            return ModelRune.runeModels[key] as ModelRune;
        }

        /**
         * 获取 科技 model
         */
        public function getModelScience(key:String):ModelScience {
            if (!key) {
                return null;
            }
            if (!ModelScience.modelSciences.hasOwnProperty(key)) {
                ModelScience.modelSciences[key] = new ModelScience();
                (ModelScience.modelSciences[key] as ModelScience).initData(key, ModelScience.getConfig(key));
            }
            return ModelScience.modelSciences[key] as ModelScience;
        }

        /**
         * 获取 皮肤 model
         */
        public function getModelSkin(key:String):ModelSkin {
            if (!key) {
                return null;
            }
            if (!ModelSkin.skinModels.hasOwnProperty(key)) {
                ModelSkin.skinModels[key] = new ModelSkin(key);
                (ModelSkin.skinModels[key] as ModelSkin).initData(key, ConfigServer.hero_skin[key]);
            }
            return ModelSkin.skinModels[key] as ModelSkin;
        }

        /*
           ##################################以上  获取model    ####################################
         */




        /*
           ##################################    服务器推送消息    ####################################
         */

        private function pushMsgHandler():void {
            NetSocket.instance.registerHandler("push_msg", new Handler(this, function(np:NetPackage):void {
                if (ModelGuide.forceGuide()) {
                    return;
                }
                var data:* = np.receiveData;
                var msg:String = data.msg;
                var code:int = data.code;
                if (code) {
                    msg += Tools.getObjValue(Tools.sMsgDic, "_push_msg_" + code, "");
                }
                ViewManager.instance.showAlert(msg, null, null, "", true);
            }));
        }

        private function updateUserHandler():void {
            NetSocket.instance.registerHandler("update_user", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
                // Trace.log("=============服务器主动推送的刷新用户数据");
            }));
        }

        private function getRecords():void {
            NetSocket.instance.registerHandler("push_records", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
                ModelHappy.instance.event(ModelHappy.EVENT_UPDATE_SPARTA);
            }));
        }

        private function getCoinByOffice():void {
            NetSocket.instance.registerHandler("pay_son", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
                ViewManager.instance.showIcon({"coin": np.receiveData.add_coin}, Laya.stage.width / 2, Laya.stage.height / 2);
                ViewManager.instance.showTipsTxt(Tools.getMsgById("530077", [np.receiveData.king_name, np.receiveData.add_coin]));
            }));
        }

        private function buildCityBuildHandler():void {
            var _this:* = this;
            NetSocket.instance.registerHandler("build_city_build", new Handler(this, function(np:NetPackage):void {
                ModelOfficial.updateCityBuild(np.receiveData);
                _this.event(EventConstant.UPDATE_BUILD, {cid: np.receiveData.cid, bid: np.receiveData.bid, name: np.receiveData.uname});
                if (np.receiveData.bid) {
                    if (ConfigServer.city_build.great_wall) {
                        if (ConfigServer.city_build.great_wall.indexOf(np.receiveData.bid) != -1) {
                            ModelNewMilepost.instance.checkNewMilepost();
                        }
                    }
                }
            }));
        }


        private function bugMSGHandler():void {
            NetSocket.instance.registerHandler("bug_msg", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelChat.isNewBugMSG = true;
                ModelManager.instance.modelUser.event(ModelUser.EVENT_USER_UPDATE, [{"user": ""}, true]); //通知红点刷新
                event(ModelGame.EVENT_UPDAET_BUG_MSG);
            }));
        }

        /**
         * 聊天消息监听
         */
        private function chatHandler():void {
            var _this:* = this;
            NetSocket.instance.registerHandler("chat", new Handler(this, function(np:NetPackage):void {
                ////Trace.log("=======收到聊天消息",np.receiveData);
                if (np.receiveData is Array) {
                    ModelManager.instance.modelChat.acceptMSG(np.receiveData);
                    ModelManager.instance.modelOfficel.setCallCD(np.receiveData[5]);
                    if (np.receiveData[1] == "build_ballista") {
                        var s1:String = StringUtil.htmlFontColor(Tools.getMsgById("country_" + np.receiveData[3][0]), ColorManager.getUIColor('COUNTRY_COLORS')[np.receiveData[3][0]]);
                        var s2:String = Tools.getMsgById(ConfigServer.country_pvp.ballista[np.receiveData[3][1]].name);
                        _this.event(EVENT_SHOW_BALLISTA_MSG, [np.receiveData[3][0], s1, s2]); //[国家名，建造车的名字]
                            // Trace.log("收到攻城车建造信息",s1,s2);
                    }
                } else if (np.receiveData.msg) {
                    ViewManager.instance.showTipsTxt(np.receiveData.msg);
                }
            }));

            //跨服战聊天
            CrossServiceFacade.instance.socket.registerHandler("d.chat", Handler.create(this, function(pkg:NetPackage):void {
                Trace.log("=======收到跨服战聊天消息", pkg.receiveData);
                if (pkg.receiveData is Object) {
                    if (pkg.receiveData["user"]) {
                        CrossServiceUserModel.instance.updateUserData(pkg.receiveData);
                        ModelManager.instance.modelChat.acceptMSG(pkg.receiveData["chat"]);
                    } else {
                        ModelManager.instance.modelChat.acceptMSG(pkg.receiveData);
                    }
                } else if (pkg.receiveData.msg) {
                    //ViewManager.instance.showTipsTxt(np.receiveData.msg);
                }
            }, null, false));


        }

        /**
         * 年度战功结算推送
         */
        private function creditSettleHandler():void {
            NetSocket.instance.registerHandler("credit_settle", new Handler(this, function(np:NetPackage):void {
                //var data:Array=np.receiveData;
                ModelManager.instance.modelUser.credit_settle = np.receiveData;
                //ViewManager.instance.showView(["ViewCreditResult",ViewCreditResult],data);
            }));
        }

        private function clubAlienFight():void { //异邦来访 /开始
            var _this:* = this;
            NetSocket.instance.registerHandler(ModelClub.SOCEKET_GET_CLUB_ALIEN, new Handler(this, function(np:NetPackage):void {
                if (np.receiveData is Boolean) {
                    //Trace.log("异邦来访信息有更改");
                    NetSocket.instance.send(ModelClub.SOCEKET_GET_CLUB_ALIEN, {}, Handler.create(_this, function(pp:NetPackage):void {
                        ModelManager.instance.modelUser.updateData(pp.receiveData);
                        ModelManager.instance.modelClub.event(ModelClub.EVENT_ALIEN_MSG);
                    }));
                } else {
                    ////Trace.log("get_guild_alien推送了一条非boolean返回值的消息");
                    //ModelManager.instance.modelUser.updateData(np.receiveData);
                    //ModelManager.instance.modelGuild.event(ModelGuild.EVENT_ALIEN_MSG);	
                }
            }));
        }

        private function clubAlienChange():void { //异邦来访  加入/退出
            NetSocket.instance.registerHandler("club_alien_join_quit", new Handler(this, function(np:NetPackage):void {
                if (np.receiveData) {
                    ModelManager.instance.modelClub.updateAlien(np.receiveData);
                    ModelManager.instance.modelClub.event(ModelClub.EVENT_ALIEN_MSG);
                    ModelManager.instance.modelUser.event(ModelUser.EVENT_UPDATE_BTM_BTN);
                }
            }));
        }

        private function clubRedBagUpdate():void { //兵团里有人发红包了(有团员充值了就能收到)
            var _this:* = this;
            NetSocket.instance.registerHandler("get_club_redbag", new Handler(this, function(np:NetPackage):void {
                if (np.receiveData is Boolean) {
                    NetSocket.instance.send("get_club_redbag", {}, new Handler(_this, function(nnp:NetPackage):void {
                        ModelManager.instance.modelUser.updateData(nnp.receiveData);
                        ModelManager.instance.modelClub.event(ModelClub.EVENT_COUNTRY_REDBAG);
                        ModelManager.instance.modelUser.event(ModelUser.EVENT_USER_UPDATE, [{"country_club": ""}, true]); //通知红点刷新
                    }));
                        // Trace.log("有国家红包了");
                }
            }));
        }

        /**
         * 收到任务推送
         */
        private function pushTask():void {
            NetSocket.instance.registerHandler("push_task", new Handler(this, function(np:NetPackage):void {
                // false && Trace.log(">>>>>> 任务更新了！！！" + np.receiveData.user.task);
                ModelManager.instance.modelUser.updateData(np.receiveData);
            }));
        }

        /**
         * 收到官邸升级礼包推送
         */
        private function pushLevelupGift():void {
            NetSocket.instance.registerHandler("levelup_gift", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
            }));
        }

        /**
         * 收到成就推送
         */
        private function pushEffort():void {
            NetSocket.instance.registerHandler("push_effort", new Handler(this, function(np:NetPackage):void {
                // false && Trace.log(">>>>>> 成就更新了！！！" + np.receiveData.user.effort);
                ModelManager.instance.modelUser.updateData(np.receiveData);
            }));
        }

        /**
         * 收到拍卖推送
         */
        private function pushAuction():void {
            NetSocket.instance.registerHandler("push_auction", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
            }));
        }

        /**
         * 收到密旨推送
         */
        private function pushNewTask():void {
            NetSocket.instance.registerHandler("push_new_task", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
                ModelManager.instance.modelGame.event(EVENT_NEW_TASK_PUSH);
            }));
        }

        /**
         * 赛季英雄数据推送
         */
        private function pushHonour():void {
            NetSocket.instance.registerHandler("push_honour", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
            }));
        }

        /**
         * 禁言推送
         */
        public function pushFreezeDo():void {
            NetSocket.instance.registerHandler("freeze_do", new Handler(this, function(np:NetPackage):void {
                Trace.log("freeze_do:", np.receiveData);
                ModelManager.instance.modelChat.freezeDoCallBack(np.receiveData);
            }));
        }

        /**
         * 答题推送
         */
        public function pushAsk():void {
            NetSocket.instance.registerHandler("push_ask", new Handler(this, function(np:NetPackage):void {
                ModelFestival.instance.modelFestivalAsk.checkData(np.receiveData);
            }));
        }

        /**
         * 答题推送
         */
        public function pushAnswerLog():void {
            NetSocket.instance.registerHandler("push_answer_question", new Handler(this, function(np:NetPackage):void {
                ModelFestival.instance.modelFestivalAsk.pushAnswerQuestionCallBack(np.receiveData);
            }));
        }


        private function mailGiftMsg():void { //收到系统邮件或者私聊
            var _this:* = this;
            NetSocket.instance.registerHandler("gift_msg", new Handler(this, function(np:NetPackage):void {
                //Trace.log(">>>>>> 收到邮件了！！！");
                ModelManager.instance.modelChat.isNewMail = true;
                ModelManager.instance.modelUser.event(ModelUser.EVENT_USER_UPDATE, {"user": ""}); //通知红点刷新 但是其实没有获得最新的数据
                ModelManager.instance.modelUser.event(ModelUser.EVENT_UPDATE_MAIL_SYSTEM);
                if (ModelChat.mCurUid != "") { //仅打开了私聊窗口的时候
                    ModelManager.instance.modelChat.sendSocketGetMsg();
                }
            }));
        }

        private function freeBuyMsg():void {
            var _this:* = this;
            NetSocket.instance.registerHandler("free_buy", new Handler(this, function(np:NetPackage):void {
                //Trace.log(">>>>>>> free buy推送！！！");
                //ViewManager.instance.showTipsTxt("freeBuy推送了！！！！！！");
                NetSocket.instance.send("get_free_buy", {}, new Handler(_this, function(re:NetPackage):void {
                    ModelManager.instance.modelUser.checkFreeBuy(re.receiveData);
                    ModelManager.instance.modelUser.updateData(re.receiveData);

                    ModelFreeBuy.instance.addData();
                    //ModelManager.instance.modelUser.event(ModelUser.EVENT_ACT_TIME_OUT);
                }));
            }));
        }

        /**
         * 产业变更推送
         */
        private function getEstateMSG():void {
            NetSocket.instance.registerHandler("get_estate", new Handler(this, function(np:NetPackage):void {
                //ViewManager.instance.showTipsTxt("城市被攻陷  产业清理");
                ModelManager.instance.modelUser.updateData(np.receiveData);
                ModelEstate.removeMyEstate(np.receiveData.city_id, -1);
                //Trace.log("==========城池丢失 产业清理",np.receiveData);
            }));
        }

        /**
         * 国家宝库
         */
        private function getCountryStoreroomMSG():void {
            NetSocket.instance.registerHandler("push_country_storeroom", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
                ModelCountryStoreroom.instance.event(ModelCountryStoreroom.COUNTYR_STOREROOM_FRESH); 
                PuppetUtils.building_store.refresh();              
            }));
        }

        /**
         * 个人国家宝库
         */
        private function getUserStoreroomMSG():void {
            NetSocket.instance.registerHandler("push_user_storeroom", new Handler(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
                ModelCountryStoreroom.instance.event(ModelCountryStoreroom.COUNTYR_STOREROOM_FRESH);               
            }));
        }

        private function getFefreshGwent():void {
            C_Gwent.instance.registerFefreshGwent();
        }

        private function getFefreshGroup():void {
            C_Champion.instance.registerFefreshGroup();
        }

        private function getFefreshNGTask():void {
            C_NGTask.instance.registerFefreshNGTask();
        }

        private function getFefreshGodSale():void {
            ModelKunlunSale.instance.register_FefreshGodSale();
        }

        /*
           ##################################    以上 服务器推送消息    ####################################
         */




        /*
           ##################################    登录时调用    ####################################
         */
        public function heroCatchTimer(isFirst:Boolean = true):void { //登录时调用
            var _this:* = this;
            var fun:Function = function():void {
                var now:Number = ConfigServer.getServerTimer();
                var m:Number = Tools.getFullHourDis();
                Laya.timer.once(m + 1000, _this, heroCatchTimer, [false]);
            };
            if (isFirst) {
                ModelManager.instance.modelUser.updateData({"user": {"hero_catch": ModelManager.instance.modelUser.hero_catch}});
                fun();
            } else {
                NetSocket.instance.send("get_hero_catch", {}, new Handler(this, function(np:NetPackage):void {
                    ModelManager.instance.modelUser.updateData(np.receiveData);
                    fun();
                }));
            }
        }

        public function heroDrinkTimer(isFirst:Boolean = true):void { //登录时调用
            var _this:* = this;
            var fun:Function = function():void {
                var nextRefreshTime:Number = getDrinkNextRefreshTime();
                if (nextRefreshTime != -1) {
                    Laya.timer.once(nextRefreshTime + 1000, _this, heroDrinkTimer, [false]);
                }
            };
            if (isFirst) {
                ModelManager.instance.modelUser.updateData({"user": {"drink": ModelManager.instance.modelUser.drink}});
                fun();
            } else {
                NetSocket.instance.send("get_drink", {}, new Handler(this, function(np:NetPackage):void {
                    ModelManager.instance.modelUser.updateData(np.receiveData);
                    fun();
                }));
            }
        }

        private function getDrinkNextRefreshTime():Number {
            var drinkCfg:Object = ConfigServer.system_simple.drink;
            if (Tools.isNullObj(drinkCfg)) {
                return -1;
            }

			var timeZones:Array = drinkCfg.time;
			var nextRefreshTime:Number = -1;
			for (var index:int = 0, len:int = timeZones.length; index < len; index++) {
				var timeZone:Array = timeZones[index];
				var subRefreshTime: Number = Tools.getTodayMillWithHourAndMinute(timeZone);
				if(ConfigServer.getServerTimer() <= subRefreshTime) {
					nextRefreshTime = subRefreshTime - ConfigServer.getServerTimer();
					break;
				}
			}
			if (nextRefreshTime == -1) {
				nextRefreshTime = Tools.getTodayMillWithHourAndMinute(timeZones[0]) + Tools.oneDayMilli - ConfigServer.getServerTimer();
			}

			return nextRefreshTime;
		}

        public function getCityVisit():void { //登录时调用
            if (ModelGame.unlock(null, "map_visit").stop) {
                this.event(ModelVisit.EVENT_INIT_VISIT);
                return;
            }
            NetSocket.instance.send("get_city_visit", {}, new Handler(this, function(re:NetPackage):void {
                ModelOfficial.visit = re.receiveData;
                initVisit();
                //var refresh_time:Number=Tools.getTimeStamp(re.receiveData["refresh_time"]);
                //var now:Number=ConfigServer.getServerTimer();
                //delete re.receiveData["refresh_time"];
                ////Trace.log(refresh_time+"   "+Tools.getTimeStyle(refresh_time-now)+"后调用get_city_visit");
                //Laya.timer.once(refresh_time-now,this,function():void{
                //	NetSocket.instance.send("get_city_visit",{},new Handler(this,this.reVisit));
                //	});
            }));
        }

        /**
         * 初始化拜访数据（先调接口）
         */
        public function initVisit():void {
            ModelVisit.visitModels = {};
            var md:ModelVisit;
            for (var c1:String in ModelOfficial.visit) {
                if (c1 != "refresh_time") {
                    md = new ModelVisit();
                    md.initData(c1, ModelOfficial.visit[c1]);
                    ModelVisit.visitModels[c1] = md;
                        //Trace.log("=========初始化世界的visit", c1);
                }
            }

            for (var c2:String in ModelManager.instance.modelUser.visit) {
                var arr:Array = ModelManager.instance.modelUser.visit[c2];
                if (arr[0] != null) { //未领奖拜访
                    if (ModelVisit.visitModels[c2]) {
                        md = ModelVisit.visitModels[c2] as ModelVisit;
                        if (md.isTodayVisit() == false) {
                            md.initOldData(c2, md.visit_hid, arr[1]);
                                //Trace.log("=========未领奖拜访，且今日可拜访", c2);
                        }
                    } else {
                        md = new ModelVisit();
                        md.initOldData(c2, "", arr[1]);
                        ModelVisit.visitModels[c2] = md;
                            //Trace.log("=========未领奖拜访，且今日不可拜访", c2);
                    }

                }
            }

            this.event(ModelVisit.EVENT_INIT_VISIT);
        }

        /*
           ##################################   以上  登录时调用    ####################################
         */


        /**
         * 是否有部队 能 移动到 这个城市id
         * cfgClass = 自己的子类(参考ViewAlienHeroSend),必须继承(ViewHeroSend)
         * otherPa = 附加参数
         * troopPa 部队参数 {"one":0, "pos":0, "free":0, "power":-1, "fightType":3}
         * selectOne 单选
         * postionType -2是啥忘了 -1所有部队  0本城部队  1本城除外部队
         * isFree 空闲部队
         * fightType 不清楚干嘛的  默认是3
         */
        public function checkTroopToAction(id:*, cfgClass:Array = null, otherPa:* = null, troopPa:Object = null):Array {
            var cid:int = parseInt(id);
            var selectOne:Boolean = troopPa && troopPa["one"] ? true : false;
            var postionType:int = troopPa && troopPa["pos"] ? troopPa["pos"] : 0;
            var isFree:Boolean = troopPa && troopPa["free"] ? troopPa["free"] : false;
            var power:int = troopPa && troopPa["power"] ? troopPa["power"] : -1;
            var fightType:int = troopPa && troopPa["fightType"] ? troopPa["fightType"] : 3;
            var searchArr:Array = ModelManager.instance.modelTroopManager.getMoveTroop(cid, postionType, null, isFree);
            if (searchArr.length <= 0 && fightType == 1) {
                ViewManager.instance.showTipsTxt(Tools.getMsgById("_country43"));
                return searchArr;
            }
            ViewManager.instance.showView(Tools.isNullObj(cfgClass) ? ConfigClass.VIEW_HERO_SEND : cfgClass, [cid, searchArr, otherPa, selectOne, postionType, power, fightType]);
            return searchArr;
        }

        /**
         * 实名检查
         */
        public function checkRealNameTimeTips(gt:Number):void {
            this.gameTipsTimer = -1; //
            this.gameTimer = gt;
            this.checkGameTimeTips();
        }

        private function checkGameTimeTips():void {
            var unObj:Object = ModelGame.unlock(null, "tired_real_name");
            if (!getTieldOpen() || !unObj.visible) {
                return;
            }
            var arr:Array = checkTired(this.gameTimer);
            //
            var tipNum:Number = arr[arr.length - 1] * 60;
            if (this.gameTimer >= tipNum && this.gameTimer >= this.gameTipsTimer) {
                var isFirst:Boolean = this.gameTipsTimer < 0;
                if (this.gameTimer % tipNum == 0) {
                    this.gameTipsTimer = (Math.ceil(this.gameTimer / tipNum) + 1) * tipNum;
                } else {
                    this.gameTipsTimer = Math.ceil(this.gameTimer / tipNum) * tipNum;
                }
                if (!isFirst) {
                    //派发提升消息
                    this.event(EVENT_REAL_NAME_CHECK_TIRED_TIME, [this.gameTimer, arr[arr.length - 2]]);
                }
            }
            Laya.timer.clear(this, this.checkGameTimeTipsTimer);
            Laya.timer.once(this.gameTipsTimerDes * Tools.oneSecondMilli, this, this.checkGameTimeTipsTimer);
        }

        private function checkGameTimeTipsTimer():void {
            this.gameTimer += this.gameTipsTimerDes;
            this.checkGameTimeTips();
        }

        /**
         * 检查 异族入侵
         */
        public function checkPKnpc():void {
            var _this:* = this;
            NetSocket.instance.send(NetMethodCfg.WS_SR_GET_PK_NPC, {}, Handler.create(this, function(vo:NetPackage):void {
                ModelManager.instance.modelUser.updateData(vo.receiveData);
                _this.checkPKnpcTimer(0);
                _this.checkPKnpcCaptain(false);
                // this.checkPKnpcFightTimerStart();
            }));
            //
        }

        public function checkPKnpcTimer(firstTimer:Number = 0):void {
            Laya.timer.clear(this, this.checkPKnpc);
            if (firstTimer > 0) {
                Laya.timer.once(firstTimer, this, this.checkPKnpc);
                return;
            }
            var len:int = ConfigServer.pk_npc.alien_time.length;
            var nowMs:Number = ConfigServer.getServerTimer();
            var now0:ServerDate = new ServerDate(nowMs);
            now0.setHours(0, 0, 0, 0);
            var now0ms:Number = now0.getTime();
            var stepMs:Number = 0;
            var index:int = 0;
            for (var i:int = 0; i < len; i++) {
                stepMs = ConfigServer.pk_npc.alien_time[i][0] * Tools.oneHourMilli + ConfigServer.pk_npc.alien_time[i][1] * Tools.oneMinuteMilli;
                if (nowMs < (now0ms + stepMs)) {
                    index = i;
                    break;
                }
            }
            var des:Number = (now0ms + stepMs) - nowMs;
            if (des <= 0) {
                stepMs = (24 + ConfigServer.pk_npc.alien_time[0][0]) * Tools.oneHourMilli + ConfigServer.pk_npc.alien_time[0][1] * Tools.oneMinuteMilli;
                des = (now0ms + stepMs) - nowMs;
            }
            //异族入侵---下一次刷新间隔-
            if (des > 0) {
                Laya.timer.once(des + 1000, this, this.checkPKnpc);
            }
        }

        /**
         * 检查 异族入侵 战斗中的 队伍 状态
         */
        public function checkPKnpcFightTimerStatus():void {
            var alienAll:Object = ModelClimb.alien_my();
            var pkmd:ModelClimb;
            var num:Number = 0;
            for (var key:String in alienAll) {
                if (!ModelClimb.pk_npc_models.hasOwnProperty(key)) {
                    ModelClimb.pk_npc_models[key] = pkmd = new ModelClimb();
                    pkmd.pk_npc_setData(key);
                    pkmd.pk_npc_init();
                    num += 1;
                }
            }
            //
            if (ModelClimb.captain_my().length > 0) {
                if (ModelClimb.captain_curr().length > 0) {
                    if (ModelClimb.captain_curr_timer() > 0) {
                        if (!ModelClimb.pk_npc_models.hasOwnProperty("captain")) {
                            ModelClimb.pk_npc_models["captain"] = pkmd = new ModelClimb();
                            pkmd.pk_npc_setData("captain");
                            pkmd.pk_npc_init();
                            num += 1;
                        }
                    }
                }
            }

            ModelManager.instance.modelGame.event(EVENT_PK_NPC_CHECK_MODEL, [ModelClimb.pk_npc_models, "captain"]);

        }

        /**
         * 检查名将来袭
         */
        public function checkPKnpcCaptain(sendNet:Boolean, checkLocal:Boolean = false):void {
            Laya.timer.clear(this, this.checkPKnpcCaptain);
            if (sendNet) {
                var _this:* = this;
                NetSocket.instance.send(NetMethodCfg.WS_SR_GET_PK_NPC, {}, Handler.create(_this, function(vo:NetPackage):void {
                    ModelManager.instance.modelUser.updateData(vo.receiveData);
                    //
                    _this.checkPKnpcCaptain(false);

                    checkPKnpcCaptainLocal(checkLocal);
                }));
            } else {
                var currEndTimer:Number = ModelClimb.captain_curr_timer();
                if (currEndTimer > 0) {
                    var des:Number = currEndTimer - ConfigServer.getServerTimer();
                    var desMin:Number = 20000;
                    if (des >= 0 && des > desMin) {
                        desMin = des;
                    }
                    Laya.timer.once(desMin, this, this.checkPKnpcCaptain, [true]);
                }
            }
        }


        public var mPKnpcCaptainLocalData:Object;

        /**
         * 检查名将来袭是否需要弹出提示
         * "官邸升级","名将来袭领奖"时调用
         */
        public function checkPKnpcCaptainLocal(isShow:Boolean = false):Object {

            var obj:Object = null;
            var pk_npc:Object = ModelManager.instance.modelUser.pk_npc;
            var captain:Array = (pk_npc && pk_npc.captain && pk_npc.captain[0]) ? pk_npc.captain[0] : [];
            if (captain.length == 0)
                return null;

            var hid:String = captain[5];
            var cfg:Object = ConfigServer.pk_npc.captain_nameandrew[hid];
            if (!cfg[3])
                return null;

            if (!mPKnpcCaptainLocalData)
                mPKnpcCaptainLocalData = SaveLocal.getValue("captian_" + ModelManager.instance.modelUser.mUID, true);

            if (!mPKnpcCaptainLocalData)
                mPKnpcCaptainLocalData = {};

            var needSave:Boolean = false;

            if (!mPKnpcCaptainLocalData.hasOwnProperty(hid)) {
                mPKnpcCaptainLocalData[hid] = 0;
                needSave = true;
            }

            if (mPKnpcCaptainLocalData[hid] == 0) {
                obj = {"talks": cfg[3], "hid": hid};
                if (isShow) {
                    if (ModelGuide.forceGuide()) { //新手引导
                        return null;
                    }
                    GotoManager.instance.boundForCaptain();
                    needSave = true;
                    mPKnpcCaptainLocalData[hid] = 1;
                    ViewManager.instance.showView(["ViewHeroTalk2", ViewHeroTalk2], obj);
                }
            }

            if (needSave)
                SaveLocal.save("captian_" + ModelManager.instance.modelUser.mUID, mPKnpcCaptainLocalData, true);

            return obj;
        }

        public function setCaptianLoacalData(hid:String):void {
            if (mPKnpcCaptainLocalData) {
                if (!mPKnpcCaptainLocalData.hasOwnProperty(hid) || mPKnpcCaptainLocalData[hid] != 1) {
                    mPKnpcCaptainLocalData[hid] = 1;
                    SaveLocal.save("captian_" + ModelManager.instance.modelUser.mUID, mPKnpcCaptainLocalData, true);
                }
            }

        }

        public function getWorldLv():void {
            NetSocket.instance.send(NetMethodCfg.WS_SR_GET_WORLD_LV, {}, Handler.create(this, function(re:NetPackage):void {
                ModelManager.instance.modelUser.updateData({user: {world_lv: re.receiveData}});
                ModelManager.instance.modelGame.event(ModelGame.EVENT_NEW_DAY_COM_ON);
            }));
        }


        private static var sUnlockObj:Object = {};

        /**
         * 功能锁
         * sUnlockObj 如果visible = true && gray = false 就存下来，下次就不再计算了
         */
        public static function unlock(btn:*, key:String, tips:Boolean = false):Object {
            var reObj:Object = {};


            if (sUnlockObj[key]) {
                if (sUnlockObj[key]["visible"] && !sUnlockObj[key]["gray"]) {
                    reObj["visible"] = true;
                    reObj["gray"] = false;
                    reObj["stop"] = false;
                    reObj["text"] = "";
                    if (btn) {
                        btn.visible = true;
                        btn.gray = false;
                        btn.off(Event.CLICK, ModelManager.instance.modelGame, ModelManager.instance.modelGame.click_unlock);
                    }
                    return reObj;
                }
            }

            // #[是否显示-1==完全不显示0==根据显示等级1==完全显示,显示等级0~n,是否灰色0~1,等级限制0~n],建筑类型,文字说明,[pf组]
            var cfgObj:Object = ConfigServer.system_simple;
            var cfgArr:Array = ConfigServer.system_simple.func_open[key];

            if (cfgArr == null) {
                Trace.log("error  ", key);
                reObj["visible"] = true;
                reObj["gray"] = false;
                reObj["stop"] = false;
                reObj["text"] = "";
                sUnlockObj[key] = {"visible": true, "gray": false};
                return reObj;
            }

            var _showStatus:int = cfgArr[0]; //-1完全不显示  1完全显示  0根据等级显示
            var _showLv:int = cfgArr[1]; //a[0] = 0时，等级小于这一位时不显示
            var _grayStatus:int = cfgArr[2]; //0置灰  大于0判断等级
            var _grayLv:int = cfgArr[3]; //a[2] > 0时，等级小于这一位则置灰。
            var _bid1:String = cfgArr[4]; //判断等级的建筑id
            var _textId:String = cfgArr[5]; //a[2] = 0时，弹出的提示。a[2] != 0，则弹出升级a[4]建筑等级(190008)
            var _pf:Array = cfgArr[6]; //命中pf时,a[0]只能是-1或1
            var _bid2:String = cfgArr[7] ? cfgArr[7] : ""; //需要解锁这个建筑(190012)
            var _merge:Array = cfgArr[8] ? cfgArr[8] : null; //合服和天数要求

            var lv:int = Tools.isNullString(_bid1) ? 1 : ModelManager.instance.modelInside.getBuildingModel(_bid1).lv;

            if (_merge) {
                var mergeNum:Number = ModelManager.instance.modelUser.mergeNum;
                var openDays:Number = ModelManager.instance.modelUser.loginDateNum;
                if ((mergeNum < _merge[0]) || (mergeNum == _merge[0] && openDays < _merge[1])) {
                    reObj["visible"] = false;
                    reObj["stop"] = true;
                    return reObj;
                }
            }

            reObj["visible"] = (_showStatus == 1) ? true : ((_showStatus < 0) ? false : (lv >= _showLv));
            reObj["gray"] = (_grayStatus == 0) ? true : (lv < _grayLv);

            if (_pf && _pf.indexOf(ModelManager.instance.modelUser.pf) != -1) {
                reObj["visible"] = (_showStatus == 1) ? false : ((_showStatus == -1) ? true : false);
                reObj["stop"] = (!reObj["visible"] || reObj["gray"]) ? true : false;
                return reObj;
            }

            var tipStr:String = "";
            if (reObj.gray) {
                if (_grayStatus == 0)
                    tipStr = Tools.getMsgById(_textId + "");
                else
                    tipStr = Tools.isNullString(_bid1) ? "" : Tools.getMsgById("190008", [ModelManager.instance.modelInside.getBuildingModel(_bid1).getName(), _grayLv]);
            } else {
                if (!Tools.isNullString(_bid2)) {
                    if (ModelManager.instance.modelInside.getBuildingModel(_bid2).lv == 0) {
                        tipStr = Tools.getMsgById("190012", [ModelManager.instance.modelInside.getBuildingModel(_bid2).getName()]);
                        reObj["gray"] = true;
                    }

                }
            }
            reObj["stop"] = (!reObj["visible"] || reObj["gray"]) ? true : false;
            reObj["text"] = tipStr;
            //
            if (btn) {
                btn.gray = reObj.gray;
                btn.visible = reObj.visible;
                btn.off(Event.CLICK, ModelManager.instance.modelGame, ModelManager.instance.modelGame.click_unlock);
                if (btn.gray && tipStr) {
                    btn.on(Event.CLICK, ModelManager.instance.modelGame, ModelManager.instance.modelGame.click_unlock, [tipStr]);
                }
            }
            //
            if (tips && reObj.gray && tipStr != "") {
                ModelManager.instance.modelGame.click_unlock(tipStr);
            }
            //
            sUnlockObj[key] = {"visible": reObj.visible, "gray": reObj.gray};
            return reObj;
        }

        public function click_unlock(str:String):void {
            ViewManager.instance.showTipsTxt(str);
        }

        /**
         * 红点,显示,删除
         * @param spt red容器
         * @param visible 是否显示
         * @param xy 备用位置坐标[x,y];
         */
        public static function redCheckOnce(spt:*, visible:Boolean = false, xy:Array = null, scale:int = 1):Boolean {
            var img:*;
            if (spt && spt.numChildren > 0) {
                img = spt.getChildByName("_red_check_img_");
            }
            if (visible && spt) {
                if (!img) {
                    img = new Image(AssetsManager.getAssetsUI("bg_icon_03.png"));
                    img.name = "_red_check_img_";
                    spt.addChild(img);
                    img.x = xy && xy[0] ? xy[0] : (spt.width - img.width);
                    img.y = xy && xy[1] ? xy[1] : 0;
                    img.scaleX = img.scaleY = scale;
                }
            } else {
                if (img) {
                    (img as Image).removeSelf();
                }
            }
            return visible;
        }

        public static function getTieldOpen():Boolean {
            var b:Boolean = false;
            if (!Tools.isNullString(ModelManager.instance.modelUser.mUserCode)) {
                //230000198311080916
                var year:Number = parseInt(ModelManager.instance.modelUser.mUserCode.substr(6, 4));
                var mt:Number = parseInt(ModelManager.instance.modelUser.mUserCode.substr(10, 2));
                var dt:Number = parseInt(ModelManager.instance.modelUser.mUserCode.substr(12, 2));
                //
                var now:ServerDate = new ServerDate(ConfigServer.getServerTimer());
                var ny:Number = now.getFullYear();
                var nm:Number = now.getMonth() + 1;
                var nd:Number = now.getDate();
                //
                var yd:Number = ny - year;
                //
                if (yd > 18) {

                } else if (yd == 18) {
                    if (mt > nm) {

                    } else if (mt == nm) {
                        if (dt >= nd) {

                        } else {
                            b = true;
                        }
                    } else {
                        b = true;
                    }
                } else {
                    b = true;
                }
                    // //Trace.log(year,mt,dt,ny,nm,nd);
            } else {
                b = true;
            }
            return b;
        }

        /**
         * 快速 获取 疲劳 时间对应 配置
         */
        public static function getTiredCfg():Array {
            if (getTieldOpen()) {
                return checkTired(ModelManager.instance.modelGame.gameTimer);
            }
            return ConfigServer.system_simple.tired_point[0];
        }

        /**
         * 快速 获取 当前收益率
         */
        public static function getCurrProfitRate():Number {
            return getTiredCfg()[0];
        }

        /**
         * 检测疲劳,时间,对应,配置
         */
        public static function checkTired(cd:Number):Array {
            var cfg:Array = ConfigServer.system_simple.tired_point;
            var len:int = cfg.length;
            var gp:Array;
            var max:Number = 0;
            var min:Number = 0;
            for (var i:int = 0; i < len - 1; i++) {
                gp = cfg[i];
                max = gp[2] * 60;
                if (cd < max) {
                    break;
                }
            }
            return cfg[i];
        }

        /**
         * 点击地图上的英雄切磋
         */
        public function clickHeroCatch(cid:String, v:Vector2D):void {
            var arr:Array = ModelManager.instance.modelUser.hero_catch.hero_list;
            var n:int = -1;
            for (var i:int = 0; i < arr.length; i++) {
                if (cid == arr[i][1]) {
                    n = i;
                    break;
                }
            }
            if (n == -1) {
                return;
            }
            ViewManager.instance.showView(ConfigClass.VIEW_HERO_CATCH, [n, v]);
        }

        /**
         * 点击地图上的英雄酒宴
         */
        public function clickHeroDrink(cid:String, v:Vector2D):void {
            var arr:Array = ModelManager.instance.modelUser.drink.drink_list;
            var n:int = -1;
            for (var i:int = 0; i < arr.length; i++) {
                if (cid == arr[i][1]) {
                    n = i;
                    break;
                }
            }
            if (n == -1) {
                return;
            }
            ViewManager.instance.showView(ConfigClass.VIEW_HERO_DRINK, [n, v]);
        }
        

        /*####################以上  大地图上的点击方法调用########################*/



        public function clearHeroIcon():void {
            AssetsManager.mHeroBigIconAssets.clearAll();
            AssetsManager.mHeroSmIconAssets.clearAll();
        }

        public function checkBaseBuildUnlockFunc(bmd:ModelBuiding, index:int):void {
            if (bmd.isBase()) {
                var arr:Array = bmd.unlockFunc(bmd.lv);
                var award:Boolean = true;
                if (arr) {
                    if (index < arr.length) {
                        award = false;
                        ViewManager.instance.showViewEffect(BuildingFuncOpen.getEffect(bmd, index));
                    }
                }
                var suffix:Number = ModelManager.instance.modelUser.getUseFunctionByKey('use_newpreward');
                var lvup_reward:Object = ConfigServer.system_simple['building_lvup_reward' + (suffix || '')];
                if (lvup_reward[bmd.lv]) {
                    var getFateBoxLv:int = ConfigServer.system_simple.lvup_hero; // 获取宿命自选箱等级
                    var hasFateBox:Boolean = bmd.lv === getFateBoxLv || lvup_reward[bmd.lv].hasOwnProperty(ModelProp.ITEM_FATE);
                    if (award && hasFateBox) {
                        ViewManager.instance.showView(ConfigClass.VIEW_HERO_CHOOSE);
                        return;
                    }
                }
                award && ModelGame.checkBaseBuildUpgradeGuide(bmd);
            }
        }

        public static function checkBaseBuildUpgradeGuide(bmd:ModelBuiding):void {
            var reward:Object = ConfigServer.ploy['levelup']['reward'][bmd.lv];
            var giftReward:Object = ConfigServer.ploy['levelup_gift'];
            if (reward) {
                GotoManager.boundForPanel(GotoManager.VIEW_BASE_LEVEL_UP, String(bmd.lv));
            } else if (giftReward && giftReward['reward'][bmd.lv]) {
                LevelupGiftModelManager.instance.CreateViewByLv(bmd.lv);
            } else {
                ModelGuide.executeGuide();
            }


        }

        /*####################封地建筑等级增加次数的方法########################*/

        /**
         * 获得商店购买次数 treasuer_shop
         */
        public function getShopTimes(shopId:String, bId:String, blv:Number):Number {
            var o:Object = ConfigServer.shop;
            if (o.hasOwnProperty(shopId)) {
                var oo:Object = o[shopId];
                if (oo.all_limit != -1) {
                    if (oo.add_limit && bId == oo.add_limit[0]) {
                        return oo.all_limit + blv * oo.add_limit[1];
                    }
                }
            }
            return oo.all_limit;
        }

        ///允许显示小loading动画
        public static var isShowLoadingAni:Boolean = true;
        public static var sLockDic:Object = {};

        /**
         * 屏幕锁屏
         * @param key = 唯一标示
         * @param lock = 锁/解锁
         * @param forceUnlock = 强制解锁
         */
        public static function stageLockOrUnlock(key:*, lock:Boolean, forceUnlock:Boolean = false):void {
            if (lock) {
                ModelGame.sLockDic[key] = unlock;

            } else {
                if (ModelGame.sLockDic[key]) {
                    delete ModelGame.sLockDic[key];
                }
            }
            if (forceUnlock) {
                ModelGame.sLockDic = {};
            }
            ModelManager.instance.modelGame.event(ModelGame.EVENT_STAGE_LOCK_UNLOCK, ModelGame.sLockDic);
        }

        /**
         * 通过名称打开第一封邮件
         */
        public function showMailByTitle(_id:String):void {
            NetSocket.instance.send("get_msg", {}, Handler.create(this, function(np:NetPackage):void {
                ModelManager.instance.modelUser.updateData(np.receiveData);
                var sysData:Array = [];
                var userData:Object = np.receiveData.user.msg;
                var a:Array = userData.sys;
                //var index:int=-1;
                for (var i:int = 0; i < a.length; i++) {
                    var o:Object = {};
                    var d:Array = a[i];
                    o["title"] = d[0];
                    o["info"] = d[1];
                    o["gift"] = d[2];
                    o["time"] = d[3];
                    o["paixu"] = Tools.getTimeStamp(d[3]);
                    o["index"] = i;
                    o["isOpen"] = d[4];
                    var s:String = d[5] + "";
                    o['id'] = s.indexOf("|") != -1 ? s.split("|")[0] : s;
                    sysData.push(o);
                }
                //sysData.sort(MathUtil.sortByKey("paixu", true, false));
                for (var j:int = 0; j < sysData.length; j++) {
                    if (sysData[j] && sysData[j].isOpen == 0 && sysData[j].title == _id) {
                        ViewManager.instance.showView(ConfigClass.VIEW_MAIL_CONTENT, sysData[j]);
                        break;
                    }
                }
            }));
        }

        /**
         * gids != "" 时,pids无效
         * salePid 是否用抵扣券
         */
        public static function mbPay(pids:String, gids:String = "", salePid:Boolean = false):void {
            if (ModelManager.instance.modelMB.isCanMBPay(pids)) {
                ViewManager.instance.showView(ConfigClass.VIEW_MB_PAY, [pids, gids, salePid]);
            } else if (ModelManager.instance.modelMB.isCanLCoinPay(pids)) {
                ViewManager.instance.showView(ConfigClass.VIEW_LCOIN_PAY, [pids, gids, salePid]);
            } else {
                toPay(pids, gids, salePid);
            }
        }

        /**
         * gids != "" 时,pids无效
         * salePid 是否用抵扣券
         */
        public static function toPay(pids:String, gids:String = "", salePid:Boolean = false, mbPayHandler:* = null):void {
            if (mbPayHandler) {
                mbPayHandler.run();
                return;
            }
            var payObj:Object;
            var payCfg:Object = ConfigServer.pay_config_pf[pids];
            var envN:Number = ConfigServer.system_simple.wx_pay_test ? ConfigServer.system_simple.wx_pay_test : 0;
            var ios_url:String = "";
            payObj = {pid: pids,
                    zone: ModelManager.instance.modelUser.zone,
                    uid: ModelManager.instance.modelUser.mUID,
                    pf: ConfigApp.pf,
                    buyQuantity: payCfg[0] * 10,
                    env: envN,
                    cfg: payCfg,
                    "gids": salePid ? pids + "_" + gids : gids,
                    url: ios_url,
                    channel: ConfigApp.pf_channel,
                    mbPay: mbPayHandler};
            if (ConfigApp.logpay) {
                payObj["pfcfg"] = ConfigServer.system_simple.pay[payObj.pf];
                NetHttp.instance.send("user_zone.log_pay", payObj);
            }
            //	
            var isSelfPay:Boolean = ConfigApp.payIsSelf();
            //防沉迷判断。
            ModelAntiAddiction.instance.pay(pids, new Handler(ModelGame, function():void {
                if (isSelfPay) {
                    ViewManager.instance.showView(ConfigClass.VIEW_PAY_SELF, payObj);
                } else {
                    Platform.pay(payObj, false);
                }
            }));
        }

        /**
         * ptype: 惊喜礼包  surprise_gift
         * ptype: 新惊喜礼包  surprise_gift_new
         * ptype: 新惊喜礼包  surprise_gift_day
         * ptype: 新惊喜礼包  surprise_gift_ever
         * ptype: 抵扣券    sale_pay
         * ptype: 皮肤币    pay_skincoin
         * ptype: 私人定制  optional_ones
         * ptype: 节日限定  limit_recharge
         * salePid 是否用抵扣券
         */
        public static function tryToPay(pids:String, ptype:String = "", gids:String = "", salePid:Boolean = false):void {
            var pid:String = gids ? (salePid ? pids + "_" + gids : gids) : pids;
            NetSocket.instance.send("try_to_pay", {"pid": pid, "ptype": ptype}, new Handler(null, function(np:NetPackage):void {
                if (np.receiveData) {
                    mbPay(pids, gids, salePid);
                } else {
                    ViewManager.instance.showTipsTxt("pay error");
                }
            }));
        }

        /**
         * 是否弹出充值提醒
         * (目前只有日服需要)
         */
        public static function isOpenPayAlert():Boolean {
            var o:Object = SaveLocal.getValue(SaveLocal.KEY_PAY_ALERT + ModelManager.instance.modelUser.mUID, true);
            if (o && o["time"]) {
                return false;
            }
            var cfg:Array = ConfigServer.system_simple.pay_alert_pf;
            if (cfg && cfg.indexOf(ConfigApp.pf) != -1) {
                return true;
            }
            return false;
        }

        /**
         * 更新兵种研究的锤子（每秒调用）
         */
        public function updateArmyScienceHammer():void {
            ModelArmyUpgrade.updateHarmmer();
        }

        /**
         * 根据参数  返回 modelEstate、ModelVisit、ModelCityBuild
         */
        public function getModelWork(obj:Object):* {
            if (obj.hasOwnProperty("estate_obj")) {
                var estate:ModelEstate = ModelManager.instance.modelGame.getModelEstate(obj.estate_obj.cid, obj.estate_obj.estate_index);
                return estate;
            } else if (obj.hasOwnProperty("visit_obj")) {
                var visit:ModelVisit = ModelManager.instance.modelGame.getModelVisit(obj.visit_obj.cid);
                return visit;
            } else if (obj.hasOwnProperty("cb_obj")) {
                var cb:ModelCityBuild = ModelManager.instance.modelGame.getModelCityBuild(obj.cb_obj.cid, obj.cb_obj.bid);
                return cb;
            }
            return null;
        }




    }

}
