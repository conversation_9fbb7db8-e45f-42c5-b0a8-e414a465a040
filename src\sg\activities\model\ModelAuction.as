package sg.activities.model {
    import sg.model.ViewModelBase;
    import sg.cfg.ConfigServer;
    import sg.manager.ModelManager;
    import sg.model.ModelUser;
    import sg.utils.Tools;
    import sg.model.ModelHero;
    import sg.net.NetMethodCfg;
    import sg.net.NetPackage;
    import sg.manager.ViewManager;
    import laya.utils.Handler;
    import sg.boundFor.GotoManager;
    import sg.view.hero.ViewAwakenHero;
    import sg.utils.TimeHelper;
    import sg.utils.ObjectUtil;
    import sg.net.NetSocket;
    import laya.maths.MathUtil;
    import sg.activities.view.AuctionRefund;
    import sg.guide.model.ModelGuide;
    import sg.utils.ArrayUtil;
    import sg.cfg.ConfigClass;
    import sg.cfg.ConfigApp;
    import sg.activities.view.auction.AuctionController;
    import sg.view.menu.PanelControl;

    public class ModelAuction extends ViewModelBase {
        public static const STATE_BEFORE:String = 'before'; // 拍卖尚未开始
        public static const STATE_AUCTION:String = 'auction'; // 拍卖开始
        public static const STATE_BUY:String = 'buy'; // 拍卖结束 可以购买
        public static const STATE_SHOW:String = 'show'; // 拍卖结束 展示得主
        public static const STATE_ENDED:String = 'end'; // 购买结束或展示结束

        public static const STATE_REFRESH_HERO:String = 'fresh_hero'; // 刷新右侧英雄信息
        public static const HERO_LIST_REFRESH:String = 'HERO_LIST_REFRESH'; //刷新左侧英雄

        // 单例
        private static var sModel:ModelAuction = null;

        public static function get instance():ModelAuction {
            return sModel ||= new ModelAuction();
        }

        public var cfg:Object;
        public var foreshowTime:int = 0; // 预告时间
        public var noticeTime_begin:int = 0; // 开始提醒时间
        public var noticeTime_end:int = 0; // 结束提醒时间
        public var beginTime:int = 0; // 拍卖开始时间
        public var endTime:int = 0; // 拍卖结束时间
        public var latestTime:int; // 落锤时间
        public var closeTime:int; // 入口关闭时间
        public var actId:int; // 当前活动ID
        public var nextId:int; // 下一次活动ID
        public var buyData:Array; // 个人拍卖数据
        public var sData:Array; // 拍卖数据（服务器）
        public var bidUids:Array = []; // 两个礼包最高出价人的UID
        public var bidUnames:Object = {}; // 两个礼包最高出价人的名字
        public var bidCountrys:Object = {}; // 两个礼包最高出价人的国家
        public var endFlags:Array = [null, null]; // 记录两个拍卖是否结束
        public var owner_contents:Array; // 两个礼包最高出价人的购买信息
        private var minAucIndex:int = -1; //正在拍卖的第一个的index

        public function ModelAuction() {
            // 获取配置
            this.cfg = ConfigServer.ploy['auction'];
            haveConfig = Boolean(cfg);
            nextId = Infinity;
        }

        /**
         * 更新个人的拍卖数据
         */
        override public function refreshData(data:*):void {
            if (!haveConfig)
                return;
            // 刷新时间
            beginTime = Tools.getTodayMillWithHourAndMinute(cfg.begin_time);
            latestTime = Tools.getTodayMillWithHourAndMinute(cfg.latest_time);
            var library:Object = cfg.library;

            var modelUser:ModelUser = ModelManager.instance.modelUser;
            var id:int = modelUser.getGameDate();
            var oneDayMilli:int = Tools.oneDayMilli;
            var open_date:int = Tools.getTimeStamp(Tools.change2Deviation(cfg.open_date)); // 活动上架时间
            var gameServerStartTimer:Number = modelUser.gameServerStartTimer; // 开服时间点
            if (open_date > gameServerStartTimer) {
                id -= Math.floor((open_date - gameServerStartTimer) / oneDayMilli);
            }
            var tempId:int = actId;
            actId = library[id] ? id : -1; // 活动ID
            buyData = data[0] !== actId ? [] : data[1];
            var currentTime:int = ConfigServer.getServerTimer();
            if (actId > 0 && tempId !== actId) { // 第一次获取到活动id
                this.refreshGlobalData({'data': sData, 'open_days': actId});

                // 活动开始时刷新数据
                currentTime < beginTime && Laya.timer.once(beginTime - currentTime, this, this.getAuction);
            }

            if (actId > 0 && this.active) { // 今天有活动 刷新界面
                nextId = Infinity;

                // 刷新定时器以及数据
                this.refreshEvent();
            } else { // 今天没活动 获取下一个活动ID
                actId = -1;
                nextId = Infinity;
                var keys:Array = ObjectUtil.keys(library);
                keys.forEach(function(key:String, index:int):void {
                    var tempNum:int = parseInt(key);
                    if (tempNum > id && tempNum < nextId) {
                        nextId = tempNum;
                    }
                }, this);
                if (nextId !== Infinity) { // 获取下次活动的开启时间
                    beginTime = Tools.getTodayMillWithHourAndMinute(cfg.begin_time) + (nextId - id) * Tools.oneDayMilli;
                    var mergeTime:int = ModelAfficheMerge.instance.mergeTime;
                    if (mergeTime && beginTime >= mergeTime) {
                        beginTime = 0;
                        actId = -1;
                        nextId = Infinity;
                    }
                }
            }

            foreshowTime = beginTime - cfg.notice[0] * Tools.oneSecondMilli; // 预告时间
            noticeTime_begin = beginTime - cfg.notice[1] * Tools.oneSecondMilli; // 提醒时间

            // 计算结束的通知时间（根据配置的开始时间和结束时间进行推导）
            noticeTime_end = beginTime + Tools.getTodayMillWithHourAndMinute(cfg.end_time) - Tools.getTodayMillWithHourAndMinute(cfg.begin_time) - cfg.add_time[0] * Tools.oneSecondMilli;

            if (beginTime > open_date) {
                foreshowTime = open_date > foreshowTime ? open_date : foreshowTime;
                noticeTime_begin = open_date > noticeTime_begin ? open_date : noticeTime_begin;

                // 通知玩家拍卖即将开始
                if (noticeTime_begin > currentTime) {
                    Laya.timer.once(noticeTime_begin - currentTime, this, function():void {
                        if (!active || nextId !== Infinity)
                            return;
                        ViewManager.instance.showTipsTxt(Tools.getMsgById(cfg.notice[2], [TimeHelper.remainTimeStr(this.getTime())]));
                    });
                }

                // 刷新游戏入口
                if (foreshowTime > currentTime) {
                    Laya.timer.once(foreshowTime - currentTime, ModelActivities.instance, ModelActivities.instance.refreshLeftList);
                }

                // 通知拍卖即将结束
                if (noticeTime_end > currentTime) {
                    Laya.timer.once(noticeTime_end - currentTime, this, this._sendEndMsg);
                }
            }
            this.event(ModelActivities.UPDATE_DATA);
            AuctionController.instance.refreshItemState();
        }


        /**
         * 更新全局的拍卖数据
         */
        public function refreshGlobalData(data:*):void {
            if (!data || !haveConfig)
                return; // 容错
            if (!data.data || !data.data.length)
                return; // 容错
            var heroNum:int = cfg.library[data.open_days].length;
            sData = data.data.slice(0, heroNum); // 后端传过来的长度为2,但是实际上可能只卖一个英雄
            endFlags = endFlags.slice(0, heroNum);
            sData.forEach(function(arr:Array, index:int):void { // 刷新结束时间
                var t:int = Tools.getTimeStamp(arr[2]);
                endTime = endTime < t ? t : endTime;
            }, this);

            if (actId <= 0)
                return; // 今天没活动

            // 检查结束 （全服推送消息）
            this._checkEnd();

            // 检查退款
            this._checkRefund();
            this.refreshEvent();
            this.event(ModelActivities.UPDATE_DATA);
            ModelActivities.instance.refreshLeftList();
            AuctionController.instance.refreshItemState();
        }

        public function _checkEnd():void {
            endFlags.forEach(function(value:int, index:int):void {
                endFlags[index] === null && (endFlags[index] = sData[index][3]);
            });

            // 检测同时结束
            endFlags.every(function(value:int):Boolean {
                return value === 0;
            }) && sData.every(function(arr:Array):Boolean {
                var uid:int = arr[1];
                return (uid is Number) && uid > 0 && arr[3];
            }) && Laya.timer.once(Tools.oneSecondMilli, this, this._showTwoOwner);

            // 单个检查
            var _this:ModelAuction = this;
            endFlags.forEach(function(value:int, index:int):void {
                endFlags[index] === 0 && sData[index][3] && Laya.timer.once(Tools.oneSecondMilli, _this, _this._showOwner, [index], false);
            }, this);

            var currentTime:int = ConfigServer.getServerTimer();
            var remainTime:int = sData.map(function(arr:Array):int {
                return Tools.getTimeStamp(arr[2]);
            }).sort(function(a:int, b:int):int {
                return b - a;
            })[0] - currentTime;
            if (remainTime > 0) {
                Laya.timer.once(remainTime + 200, this, this.getAuction);
            }

            // 计算活动最终结束时间
            var finalRemainTime:int = remainTime + cfg.buy_time * Tools.oneSecondMilli;
            if (finalRemainTime > 0) {
                Laya.timer.once(finalRemainTime + 200, this, this._pushOverMsg);
            }
        }

        /**
         * 检查是不是我拍卖成功了
         */
        private function _checkUid(index:int):void {
            if (sData[index] && sData[index][3] && sData[index][1] == ModelManager.instance.modelUser.mUID) {
                ModelManager.instance.modelGame.showMailByTitle(cfg.msg_reward[0]);
            }
        }

        private function _pushOverMsg():void {
            if (actId === -1)
                return;
            var flag:Boolean = sData.every(function(arr:Array, index:int):Boolean { // 检测是否存在购买环节
                var topPrice:int = cfg.library[actId][index][1];
                return topPrice !== -1;
            }, this);

            var content:String = Tools.getMsgById(cfg.end_text); // 内容
            Laya.timer.once((flag ? 0.1 : 5) * Tools.oneSecondMilli, this, function():void {
                event(ModelActivities.UPDATE_DATA);
                ModelActivities.instance.refreshLeftList();
                ViewManager.instance.showHintPanel(content, null, [{'name': Tools.getMsgById('_public183')}], {delayEvent: true});
            });

            var auctionData:Object = ModelManager.instance.modelUser.records.auction;
            auctionData && this.refreshData(auctionData);

            this.refreshEvent();
        }

        /**
         * 展示礼包的拥有者
         */
        private function _showOwner(index:int):void {
            var uid:int = this.sData[index][1];
            endFlags[index] = sData[index][3];
            this._checkUid(index);
            if ((uid is Number) && uid > 0) {
                var hid:String = listData[index].hid;
                var md:ModelHero = ModelManager.instance.modelGame.getModelHero(hid);
                var heroName:String = Tools.getMsgById(md.name);
                var giftName:String = Tools.getMsgById('550029', [heroName]);
                var content:String = Tools.getMsgById(cfg.show, [bidUnames[uid], giftName]); // 内容

                ViewManager.instance.showTipsTxt(content, 4);
            }
            this.event(ModelActivities.UPDATE_DATA);
        }

        private function ws_sr_user_info(np:NetPackage):void {
            var data:Object = np.receiveData;
            var uid:int = np.otherData;
            bidUnames[uid] = data.uname;
            bidCountrys[uid] = data.country;
            this.event(ModelActivities.UPDATE_DATA);
        }

        /**
         * 同时展示两个礼包的拥有者
         */
        private function _showTwoOwner():void {
            owner_contents = [];
            endFlags.forEach(function(value:int, index:int):void {
                get_user_show(bidUids[index], index);
                endFlags[index] = sData[index][3];
                _checkUid(index);
            }, this);
            owner_contents.reverse(); // 重新排序
            ViewManager.instance.showTipsTxt(owner_contents.join('<br/>'), 4);
        }

        private function get_user_show(uid:int, index:int):void {
            var uname:String = bidUnames[uid];
            var hid:String = listData[index].hid;
            var md:ModelHero = ModelManager.instance.modelGame.getModelHero(hid);
            var heroName:String = Tools.getMsgById(md.name);
            var giftName:String = Tools.getMsgById('550029', [heroName]);
            var content:String = Tools.getMsgById(cfg.show, [uname, giftName]); // 内容
            owner_contents.push(content);
        }

        public function refreshEvent():void {
            Laya.timer.clear(this, this.refreshEvent);
            if (this.getState(0) === STATE_BEFORE) {
                this.getTime() > 0 && Laya.timer.once(this.getTime(), this, this._showStart);
            }
        }

        /**
         * 拍卖活动开始
         */
        private function _showStart():void {
            ViewManager.instance.showTipsTxt(Tools.getMsgById('550036'));
            this.refreshEvent();
        }

        /**
         * 检查是否需要退款（自己的出价是否被超越）
         */
        private function _checkRefund():void {
            var user:ModelUser = ModelManager.instance.modelUser;
            var mUID:int = parseInt(user.mUID);
            sData.forEach(function(arr:Array, index:int):void {
                var uid:int = arr[1];
                if (isNaN(uid) || uid <= 0)
                    return;
                var flag:Boolean = bidUids[index] == mUID && uid != mUID; // 竞价被超越
                var hid:String = cfg.library[user.auction.open_days][index][2].awaken[0];
                var md:ModelHero = ModelManager.instance.modelGame.getModelHero(hid);
                var title:String = Tools.getMsgById('550038', [Tools.getMsgById('550029', [Tools.getMsgById(md.name)])]);
                flag && _showRefund(title);
                if (bidUids[index] !== uid) {
                    bidUids[index] = uid;
                    var remainTime:int = Tools.getTimeStamp(sData[index][2]) - ConfigServer.getServerTimer();
                    if (remainTime > 0 && remainTime <= cfg.add_time[1] * Tools.oneSecondMilli) {
                        _sendHintMsg(index);
                    }
                    event(ModelAuction.HERO_LIST_REFRESH, [hid]);
                }
            }, this);

            var _this:ModelAuction = this;
            active && bidUids.forEach(function(uid:int):void {
                if (!bidUnames[uid]) {
                    NetSocket.instance.send(NetMethodCfg.WS_SR_USER_INFO, {uid: uid}, Handler.create(_this, _this.ws_sr_user_info), uid);
                }
            }, this);
        }

        public function get listData():Array {
            var data:Array = cfg.library[actId > 0 ? actId : nextId];
            if (!data)
                return [];
            var currentTime:int = ConfigServer.getServerTimer();
            var listArr:Array = data.map(function(arr:Array, index:int):Object {
                var giftdict:Object = arr[2];
                var hid:String = giftdict.awaken[0]; // [0] 礼包里只有一个觉醒英雄
                var md:ModelHero = ModelManager.instance.modelGame.getModelHero(hid);
                var chipNum:int = giftdict[md.itemID];
                var auctionData:Array = (sData is Array && sData[index]) ? sData[index] : [];
                return {index: index,
                        startPrice: arr[0],
                        topPrice: arr[1],
                        currentPrice: auctionData[0] || arr[0],
                        uid: auctionData[1],
                        endTime: auctionData[2],
                        auctionEnd: currentTime > Tools.getTimeStamp(auctionData[2]),
                        hid: hid,
                        chipNum: chipNum || 0};
            }, this);
            return listArr;
        }

        /**
         * 获取拍卖活动的当前状态
         */
        public function getState(index:int):String {
            var currentTime:int = ConfigServer.getServerTimer();
            if (currentTime < beginTime) {
                return STATE_BEFORE;
            }
            if (!buyData)
                return STATE_ENDED;
            if (!sData || !sData.length)
                return STATE_ENDED;
            var auctionData:Array = sData[index];
            if (!auctionData)
                return STATE_ENDED;
            var endTime2:int = Tools.getTimeStamp(auctionData[2]);
            var auctionEnd:Boolean = currentTime > endTime2 + cfg.buy_time * Tools.oneSecondMilli;
            if (auctionEnd) {
                return STATE_ENDED;
            }
            if (buyData.indexOf(index) !== -1) {
                return STATE_SHOW;
            }
            if (auctionData[1] > 0 && auctionData[3] == 1 || currentTime > endTime2) {
                if (cfg.library[actId][index][1] <= 0) {
                    return STATE_SHOW;
                }
                return STATE_BUY;
            }
            return STATE_AUCTION;
        }

        /**
         * 活动剩余时间
         */
        public function getTimeByIndex(index:int):int {
            var state:String = this.getState(index);
            var currentTime:int = ConfigServer.getServerTimer();
            var auctionData:Array = sData[index];
            switch (state) {
                case STATE_BEFORE:
                    return this.getTime();
                case STATE_AUCTION:
                    return Tools.getTimeStamp(auctionData[2]) - currentTime;
                case STATE_BUY:
                case STATE_SHOW:
                    return Tools.getTimeStamp(auctionData[2]) + cfg.buy_time * Tools.oneSecondMilli - currentTime;
            }
            return 0;
        }

        /**
         * 入口倒计时
         */
        public function getTime():int {
            var currentTime:int = ConfigServer.getServerTimer();
            if (actStart) {
                return completeTime - currentTime;
            } else {
                return beginTime - currentTime;
            }
        }

        /**
         * 活动是否已经开始
         */
        public function get actStart():Boolean {
            var currentTime:int = ConfigServer.getServerTimer();
            return currentTime >= beginTime;
        }

        /**
         * 获取结束时间（拍卖结束时间和购买结束时间）
         * 优先获取拍卖结束时间
         */
        public function get completeTime():int {
            var currentTime:int = ConfigServer.getServerTimer();
            var latestTime:Number = Tools.getTodayMillWithHourAndMinute(cfg.latest_time);
            var actEndTime:int = 0;
            //拍卖中的最大结束时间
            var actMaxEndTime:int = 0;
            actId > 0 && sData && sData.forEach(function(arr:Array, index:int):void {
                var topPrice:int = cfg.library[actId][index][1];
                var t1:int = Tools.getTimeStamp(arr[2]); // 拍卖结束时间
                actEndTime = t1 > actEndTime ? t1 : actEndTime;
                if (ConfigApp.isLandscape && t1 > actMaxEndTime) {
                    actMaxEndTime = t1;
                }
                if (ConfigApp.isLandscape && currentTime >= t1 || !ConfigApp.isLandscape && currentTime >= t1 && topPrice > 0) { // 购买或单纯展示 
                    var t2:int = t1 + cfg.buy_time * Tools.oneSecondMilli; // 购买结束时间
                    actEndTime = t2 > actEndTime ? t2 : actEndTime;
                }
            });

            if (ConfigApp.isLandscape && currentTime < actMaxEndTime && currentTime < latestTime) {
                actEndTime = actMaxEndTime;
            }
            return actEndTime;
        }

        override public function get active():Boolean {
            if (!haveConfig)
                return false;
            var currentTime:int = ConfigServer.getServerTimer();
            if (actId > 0) { // 今天有活动
                return currentTime > (beginTime - cfg.notice[0] * Tools.oneSecondMilli) && currentTime < completeTime;
            }
            return nextId !== Infinity && currentTime >= (beginTime - cfg.notice[0] * Tools.oneSecondMilli);
        }

        /**
         * 检测红点
         */
        override public function get redPoint():Boolean {
            var modelUser:ModelUser = ModelManager.instance.modelUser;
            return active && sData && sData.length && sData.some(function(arr:Array, index:int):Boolean {
                var state:String = getState(index);
                var canAuction:Boolean = state === STATE_AUCTION && arr[1] != modelUser.mUID && modelUser.coin >= (arr[0] + cfg.unit); // 可以拍
                var canBuy:Boolean = state === STATE_BUY && modelUser.coin >= cfg.library[actId][index][1]; // 可以买
                return canAuction || canBuy;
            }, this);
        }

        /**
         * 获取拍卖数据
         */
        public function getAuction():void {
            ModelActivities.instance.sendMethod(NetMethodCfg.WS_SR_GET_AUCTION);
        }

        /**
         * 检查拍卖限制条件
         */
        public function checkAuctionLimit():Object {
            var limitCfg:Object = ConfigServer.system_simple.auction_limit;
            if (!limitCfg || !limitCfg.enable) {
                return {success: true}; // 功能未开启，允许拍卖
            }

            var user:ModelUser = ModelManager.instance.modelUser;

            // 检查府邸等级
            var buildingLevel:int = user.home.building001 ? user.home.building001.lv : 0;
            var requiredBuildingLevel:int = limitCfg.building_level || 25;
            if (buildingLevel < requiredBuildingLevel) {
                return {
                    success: false,
                    type: 'building',
                    message: Tools.getMsgById(limitCfg.error_msg.building, [requiredBuildingLevel])
                };
            }

            // 检查最强英雄战力
            var maxHeroPower:int = user.getBestHeroPower();
            var requiredHeroPower:int = limitCfg.hero_power || 500000;
            if (maxHeroPower < requiredHeroPower) {
                return {
                    success: false,
                    type: 'power',
                    message: Tools.getMsgById(limitCfg.error_msg.power, [requiredHeroPower])
                };
            }

            return {success: true};
        }

        /**
         * 出价
         */
        public function bid(cost:int, index:int):void {
            // 检查拍卖限制
            var limitCheck:Object = this.checkAuctionLimit();
            if (!limitCheck.success) {
                ViewManager.instance.showTipsTxt(limitCheck.message);
                return;
            }

            this.sendMethod(NetMethodCfg.WS_SR_COST_AUCTION, {cost_coin: cost, auction_index: index}, Handler.create(this, this.bidCB));
        }

        private function bidCB(np:NetPackage):void {
            var rd:Object = np.receiveData;
            if (rd.gift_dict) { // 秒杀觉醒英雄礼包
                ViewManager.instance.showRewardPanel(rd.gift_dict);
            } else {
                ViewManager.instance.showTipsTxt(Tools.getMsgById(cfg.bingo));
            }
            ModelManager.instance.modelUser.updateData(rd); // 先检查是否需要招募再更新
        }

        /**
         * 购买
         */
        public function buy(index:int, cancel:int):void {
            cancel || this.sendMethod(NetMethodCfg.WS_SR_BUY_AUCTION, {auction_index: index});
        }

        /**
         * 展示退款
         */
        private function _showRefund(tips:String):void {
            if (ModelGuide.forceGuide()) {
                return;
            }
            PanelControl.instance.auctionClosePanel();
            GotoManager.boundForPanel(GotoManager.VIEW_AUCTION);
            var _id:String = cfg.msg_surpass[0];

            NetSocket.instance.send("get_msg", {}, Handler.create(this, function(np:NetPackage):void {
                var sysData:Array = [];
                ModelManager.instance.modelUser.updateData(np.receiveData);
                var userData:Object = np.receiveData.user.msg;
                var a:Array = userData.sys;
                for (var i:int = 0; i < a.length; i++) {
                    var o:Object = {};
                    var d:Array = a[i];
                    o["title"] = d[0];
                    o["info"] = d[1];
                    o["gift"] = d[2];
                    o["time"] = d[3];
                    o["paixu"] = Tools.getTimeStamp(d[3]);
                    o["index"] = i;
                    o["isOpen"] = d[4];
                    var s:String = d[5] + "";
                    o['id'] = s.indexOf("|") != -1 ? s.split("|")[0] : s;
                    sysData.push(o);
                }
                //sysData.sort(MathUtil.sortByKey("paixu",true,false));
                var refundData:Object = null;
                for (var j:int = 0; j < sysData.length; j++) {
                    if (sysData[j] && sysData[j].isOpen == 0 && sysData[j].id == _id) {
                        refundData = sysData[j];
                        refundData.index = j;
                        refundData.tips = tips;
                        break;
                    }
                }
                if (ConfigApp.isLandscape) {
                    AuctionController.instance.isNeedAuctionRefund(refundData);
                } else {
                    if (refundData) {
                        ViewManager.instance.showView(["AuctionRefund", AuctionRefund], refundData, null, {delayEvent: true});
                    }
                }
            }));
        }

        private function _sendEndMsg():void {
            if (!active || nextId !== Infinity)
                return;
            var arr:Array = [Math.floor(cfg.add_time[0] / 60), Math.floor(cfg.add_time[1] / 60), cfg.latest_time.join(':')];
            ModelManager.instance.modelChat.sendLocalMessage([0, "auction_end", "1", arr, ConfigServer.getServerTimer()]);
        }

        private function _sendHintMsg(index:int):void {
            var hid:String = listData[index].hid;
            var md:ModelHero = ModelManager.instance.modelGame.getModelHero(hid);
            var heroName:String = Tools.getMsgById(md.name);
            var giftName:String = Tools.getMsgById('550029', [heroName]);
            var price:int = sData[index][0];
            var time:String = sData[index][2]['$datetime'].match(/(\d{2}:\d{2}):\d{2}/)[1];
            var arr:Array = [giftName, price, time];
            ModelManager.instance.modelChat.sendLocalMessage([0, "auction_end", "2", arr, ConfigServer.getServerTimer()]);
        }

        /**
         * 左侧拍卖队列显示，赤壁项目
         * @return
         */
        public function get heroAuctionList():Array {
            this.minAucIndex = -1;
            var showAll:Boolean = true;
            var data:Array = cfg.library[actId > 0 ? actId : nextId];
            if (!data)
                return [];
            var currentTime:int = ConfigServer.getServerTimer();
            var listArr:Array = listData;
            var heroStateList:Array = _getHeroData();
            var showList:Array = new Array();
            for (var index:int in heroStateList) {
                var value:Object = heroStateList[index];
                var tObj:Object = {};
                for (var i:int = 0; i < listArr.length; i++) {
                    var tData:Object = listArr[i];
                    if (tData.hid == value["hid"]) {
                        value["index"] = index;
                        value["heroData"] = tData;
                        if ((value["state"] == 1 || value["state"] === 3) && minAucIndex == -1) {
                            minAucIndex = index;
                        }
                        if (tData["topPrice"] == -1) {
                            showAll = false;
                            showList.push(value);
                        }
                    }
                }
            }
            return showAll ? heroStateList : showList;
        }

        private function _getHeroData():Array {
            var data:Array = ObjectUtil.entries(cfg.library);
            data.sort(function(a:Array, b:Array):Boolean {
                return parseInt(a[0]) - parseInt(b[0]);
            });
            var _this:* = this;
            var idArr:Array = data.map(function(arr:Array):Array {
                return arr[1].map(function(arr2:Array, index:int):Object {
                    var state:int = parseInt(arr[0]) - actId;
                    state = state ? state / Math.abs(state) : 0;
                    state += 1; // 0 已结束 1 进行中 2 未开始
                    if (state === 1) {
                        switch (getState(index)) {
                            case ModelAuction.STATE_BEFORE:
                                state = 2;
                                break;
                            case ModelAuction.STATE_BUY:
                                state = 3;
                                break;
                            case ModelAuction.STATE_ENDED:
                            case ModelAuction.STATE_SHOW:
                                state = 0;
                                break;
                        }
                    }
                    return {hid: arr2[2].awaken[0], state: state};
                }, _this);
            }, this);
            return ArrayUtil.flat(idArr);
        }

        //正在拍卖状态的第一个
        public function get curMinIndex():int {
            return this.minAucIndex;
        }

        public function isCanOpen():Boolean {
            var canOpen:Boolean = true;
            var currentTime:int = ConfigServer.getServerTimer();
            var open_date:int = Tools.getTimeStamp(cfg.open_date); // 活动上架时间
            noticeTime_begin = beginTime - cfg.notice[1] * Tools.oneSecondMilli; // 提醒时间
            if (this.active && beginTime > open_date) {
                // 通知玩家拍卖即将开始
                if (beginTime - currentTime > 0) {
                    canOpen = false;
                }
            }
            return canOpen;
        }

        public function canNotFunc():void {
            var auctionCfg:Object = ConfigServer.ploy['auction'];
            if (ConfigApp.isLandscape) {
                ViewManager.instance.showView(ConfigClass.VIEW_AUCTION_NOTICE_H, [ModelAuction.instance.heroAuctionList, auctionCfg.notice[2], ModelAuction.instance.getTime()]);
            } else {
                ViewManager.instance.showTipsTxt(Tools.getMsgById(auctionCfg.notice[2], [TimeHelper.remainTimeStr(ModelAuction.instance.getTime())]));
            }
        }

        // 拍卖监控 内部可见
        public function get bidTips():Array {
            return sData.map(function(arr:Array):String {
                var price:int = arr[0];
                var uid:int = arr[1];
                return uid + ' ' + ['魏', '蜀', '吴'][bidCountrys[uid]] + ':' + bidUnames[uid];
            });
        }
    }
}
